# 🔍 Diagnostic Steps for Swagger 500 Error

## 🚨 **Current Issue**
Swagger UI shows "Failed to load API definition" with 500 error on `/api/v3/api-docs`

## ✅ **Steps I've Taken**

1. **Disabled Swagger Configuration** - Commented out `@Configuration` in SwaggerConfig
2. **Disabled Swagger in Properties** - Set `springdoc.api-docs.enabled=false`
3. **Removed @Tag Annotations** - Commented out problematic annotations
4. **Fixed CORS Issues** - Resolved the credentials/origins conflict

## 🧪 **Diagnostic Tests**

### **Step 1: Test Basic Application**
```bash
mvn clean compile
mvn spring-boot:run
```

### **Step 2: Test Health Endpoint**
```bash
curl http://localhost:8080/api/health
```
**Expected:** Should return 200 OK with health status

### **Step 3: Test Basic API Endpoints**
```bash
# Test users endpoint
curl http://localhost:8080/api/users

# Test study materials endpoint  
curl http://localhost:8080/api/studyMaterials
```
**Expected:** Should return 200 OK (even if empty array)

### **Step 4: Check Application Logs**
Look for any errors in the console output when starting the application.

## 🔧 **If Basic APIs Work**

### **Re-enable Swagger Gradually:**

1. **Enable Swagger in Properties:**
```properties
springdoc.api-docs.enabled=true
springdoc.swagger-ui.enabled=true
springdoc.api-docs.path=/v3/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
```

2. **Test API Docs Endpoint:**
```bash
curl http://localhost:8080/api/v3/api-docs
```

3. **If API docs work, test Swagger UI:**
```
http://localhost:8080/api/swagger-ui.html
```

## 🚨 **Common Issues & Solutions**

### **Issue 1: MongoDB Connection**
**Error:** `MongoSocketOpenException` or similar
**Solution:** 
```bash
# Start MongoDB
net start MongoDB  # Windows
# or
sudo systemctl start mongod  # Linux/Mac
```

### **Issue 2: Port Conflict**
**Error:** `Port 8080 was already in use`
**Solution:** Change port in application.properties:
```properties
server.port=8081
```

### **Issue 3: Compilation Errors**
**Error:** Cannot resolve imports or annotations
**Solution:** 
```bash
mvn clean install -DskipTests
```

### **Issue 4: SpringDoc Version Conflict**
**Error:** ClassNotFoundException or NoSuchMethodError
**Solution:** Try different SpringDoc version:
```xml
<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
    <version>2.0.4</version>
</dependency>
```

## 🎯 **Alternative Swagger Setup**

If the current setup doesn't work, try this minimal configuration:

### **1. Remove Current SpringDoc Dependency**
Comment out in pom.xml:
```xml
<!-- 
<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
    <version>2.1.0</version>
</dependency>
-->
```

### **2. Add Classic Swagger Dependencies**
```xml
<dependency>
    <groupId>io.springfox</groupId>
    <artifactId>springfox-boot-starter</artifactId>
    <version>3.0.0</version>
</dependency>
```

### **3. Create Simple Swagger Config**
```java
@Configuration
@EnableSwagger2
public class SwaggerConfig {
    @Bean
    public Docket api() {
        return new Docket(DocumentationType.SWAGGER_2)
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.sparkminds.brainstorm_upsc.controller"))
                .paths(PathSelectors.any())
                .build();
    }
}
```

## 📋 **Checklist**

- [ ] Application starts without errors
- [ ] Health endpoint works (`/api/health`)
- [ ] Basic API endpoints return 200
- [ ] MongoDB is running and accessible
- [ ] No compilation errors
- [ ] CORS is properly configured
- [ ] SpringDoc dependency is compatible

## 🚀 **Next Steps**

1. **First:** Ensure basic application works without Swagger
2. **Then:** Gradually re-enable Swagger components
3. **Finally:** Add back documentation annotations

Let me know the results of the health endpoint test and any error messages you see in the console!
