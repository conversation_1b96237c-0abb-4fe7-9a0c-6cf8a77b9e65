# 📚 Swagger UI Integration - Brainstorm UPSC API

## 🚀 **Overview**
Swagger UI has been successfully integrated into the Brainstorm UPSC Spring Boot backend to provide comprehensive API documentation and testing capabilities.

## 🔗 **Access URLs**

### **Swagger UI (Interactive Documentation)**
```
http://localhost:8080/api/swagger-ui.html
```

### **OpenAPI JSON Specification**
```
http://localhost:8080/api/api-docs
```

### **OpenAPI YAML Specification**
```
http://localhost:8080/api/api-docs.yaml
```

## 📋 **Features**

### ✅ **What's Included:**
- **Interactive API Documentation** - Browse all endpoints with detailed descriptions
- **Try It Out** - Test APIs directly from the browser
- **Request/Response Examples** - See sample data for all endpoints
- **Authentication Support** - Test secured endpoints
- **Schema Documentation** - View all data models and their properties
- **Organized by Tags** - APIs grouped by functionality

### 🏷️ **API Tags/Categories:**
- **Users** - User management and authentication
- **Study Materials** - PDFs, Videos, Audio materials
- **Exams** - Mock tests and exam management
- **Questions** - Question management for exams
- **Purchases** - Payment and purchase tracking
- **Testimonials** - User success stories
- **Pricing Plans** - Subscription management
- **Notifications** - Notification system
- **Analytics** - Dashboard statistics and analytics

## 🛠️ **Configuration**

### **Dependencies Added:**
```xml
<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
    <version>2.2.0</version>
</dependency>
```

### **Application Properties:**
```properties
# Swagger/OpenAPI Configuration
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.enabled=true
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.tryItOutEnabled=true
springdoc.swagger-ui.filter=true
springdoc.show-actuator=false
```

## 📖 **Usage Guide**

### **1. Start the Application**
```bash
mvn spring-boot:run
```

### **2. Open Swagger UI**
Navigate to: `http://localhost:8080/api/swagger-ui.html`

### **3. Explore APIs**
- **Browse Endpoints** - Click on any API category to expand
- **View Details** - Click on individual endpoints to see parameters, responses
- **Test APIs** - Click "Try it out" to test endpoints directly
- **View Models** - Scroll down to see all data models/schemas

### **4. Test Authentication**
- Use the `/users/login` endpoint to authenticate
- Copy the returned user data for testing other endpoints
- Some endpoints may require user authentication

## 🎯 **Key Endpoints to Try**

### **Authentication:**
- `POST /api/users/login` - Login with email/password
- `POST /api/users/register` - Register new user

### **Study Materials:**
- `GET /api/studyMaterials` - Get all materials with filtering
- `GET /api/studyMaterials?type=PDF&subject=History` - Filter by type and subject

### **Exams:**
- `GET /api/exams` - Get all exams
- `GET /api/exams/upcoming` - Get upcoming exams

### **Analytics:**
- `GET /api/adminStats` - Get dashboard statistics
- `GET /api/userGrowthData` - Get user growth analytics

## 🔧 **Customization**

### **API Information:**
The API documentation includes:
- **Title:** Brainstorm UPSC API
- **Description:** Comprehensive REST API for UPSC exam preparation
- **Version:** 1.0.0
- **Contact:** <EMAIL>

### **Servers:**
- **Development:** http://localhost:8080/api
- **Production:** https://api.brainstormupsc.com/api

## 📝 **Sample API Testing**

### **1. Test User Login:**
```json
POST /api/users/login
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### **2. Get Study Materials:**
```
GET /api/studyMaterials?type=PDF&_limit=5
```

### **3. Create Purchase:**
```json
POST /api/purchases
{
  "userId": "1",
  "materialId": "1",
  "amount": 299,
  "paymentMethod": "razorpay"
}
```

## 🎨 **UI Features**

- **Dark/Light Theme** - Toggle between themes
- **Search** - Search for specific endpoints
- **Filter** - Filter endpoints by tags
- **Expand/Collapse** - Expand all or collapse all sections
- **Download** - Download OpenAPI specification

## 🚀 **Next Steps**

1. **Start the application** with `mvn spring-boot:run`
2. **Open Swagger UI** at `http://localhost:8080/api/swagger-ui.html`
3. **Explore the APIs** and test the endpoints
4. **Use the documentation** to understand the complete API structure

The Swagger UI provides a complete interactive documentation for your Brainstorm UPSC API, making it easy to understand, test, and integrate with the backend services! 🎯📚
