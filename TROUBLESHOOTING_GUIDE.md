# 🔧 Troubleshooting Guide - Swagger UI Issues

## 🚨 **Current Issue**
Swagger UI is not showing APIs and returning internal server error.

## ✅ **Fixes Applied**

### 1. **Simplified Swagger Configuration**
- Downgraded SpringDoc version from 2.2.0 to 2.1.0
- Simplified SwaggerConfig.java to remove complex configurations
- Removed problematic annotations from controllers

### 2. **Updated Dependencies**
```xml
<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
    <version>2.1.0</version>
</dependency>
```

### 3. **Simplified Application Properties**
```properties
# Swagger/OpenAPI Configuration
springdoc.api-docs.path=/v3/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.enabled=true
```

### 4. **Removed Complex Annotations**
- Removed `@Operation`, `@ApiResponses`, `@Parameter` annotations that were causing issues
- Kept only `@Tag` annotations for basic categorization

## 🧪 **Testing Steps**

### **Step 1: Start the Application**
```bash
mvn clean install
mvn spring-boot:run
```

### **Step 2: Test Health Endpoint**
```bash
curl http://localhost:8080/api/health
```
Expected response:
```json
{
  "status": "UP",
  "timestamp": "2025-07-09T14:30:00",
  "service": "Brainstorm UPSC API",
  "version": "1.0.0"
}
```

### **Step 3: Test API Documentation**
- **Swagger UI**: `http://localhost:8080/api/swagger-ui.html`
- **API Docs**: `http://localhost:8080/api/v3/api-docs`

### **Step 4: Test Basic API Endpoints**
```bash
# Test users endpoint
curl http://localhost:8080/api/users

# Test study materials endpoint
curl http://localhost:8080/api/studyMaterials

# Test exams endpoint
curl http://localhost:8080/api/exams
```

## 🔍 **Common Issues & Solutions**

### **Issue 1: MongoDB Connection Error**
**Solution**: Make sure MongoDB is running
```bash
# Start MongoDB (Windows)
net start MongoDB

# Start MongoDB (Mac/Linux)
sudo systemctl start mongod
```

### **Issue 2: Port 8080 Already in Use**
**Solution**: Change port in application.properties
```properties
server.port=8081
```

### **Issue 3: Swagger UI Shows Empty**
**Solution**: 
1. Clear browser cache
2. Try accessing: `http://localhost:8080/api/swagger-ui/index.html`
3. Check console for JavaScript errors

### **Issue 4: CORS Errors**
**Solution**: CORS is already configured in CorsConfig.java

## 📋 **Verification Checklist**

- [ ] MongoDB is running on localhost:27017
- [ ] Application starts without errors
- [ ] Health endpoint returns 200 OK
- [ ] Swagger UI loads at `/swagger-ui.html`
- [ ] API endpoints are visible in Swagger UI
- [ ] Basic API calls return data (even if empty)

## 🚀 **Next Steps**

### **If Swagger UI Works:**
1. Add back detailed annotations gradually
2. Test each controller individually
3. Add more comprehensive API documentation

### **If Still Having Issues:**
1. Check application logs for specific errors
2. Try accessing API docs directly: `/v3/api-docs`
3. Test with a simple REST client (Postman/Insomnia)

## 📝 **Sample API Tests**

### **Test User Registration:**
```bash
curl -X POST http://localhost:8080/api/users/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "password": "password123",
    "phone": "**********"
  }'
```

### **Test Study Materials:**
```bash
curl -X GET "http://localhost:8080/api/studyMaterials?type=PDF&_limit=5"
```

### **Test Exams:**
```bash
curl -X GET "http://localhost:8080/api/exams?type=Mock Test"
```

## 🎯 **Expected Swagger UI Structure**

Once working, you should see these API categories:
- **Users** - Authentication and user management
- **Study Materials** - Content management
- **Exams** - Test management
- **Questions** - Question management
- **Purchases** - Payment tracking
- **Testimonials** - User feedback
- **Pricing Plans** - Subscription management
- **Notifications** - Messaging system
- **Analytics** - Dashboard data
- **Health** - System health check

The simplified configuration should resolve the internal server error and display the APIs correctly! 🎉
