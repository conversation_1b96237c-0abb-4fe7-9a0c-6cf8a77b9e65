package com.sparkminds.brainstorm_upsc.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import io.swagger.v3.oas.models.tags.Tag;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class SwaggerConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Brainstorm UPSC API")
                        .description("Comprehensive REST API for Brainstorm UPSC platform - A complete backend solution for UPSC exam preparation platform with user management, study materials, exams, purchases, and analytics.")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("Brainstorm UPSC Team")
                                .email("<EMAIL>")
                                .url("https://brainstormupsc.com"))
                        .license(new License()
                                .name("MIT License")
                                .url("https://opensource.org/licenses/MIT")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:8080/api")
                                .description("Development Server"),
                        new Server()
                                .url("https://api.brainstormupsc.com/api")
                                .description("Production Server")))
                .tags(List.of(
                        new Tag().name("Users").description("User management and authentication operations"),
                        new Tag().name("Study Materials").description("Study materials management (PDFs, Videos, Audio)"),
                        new Tag().name("Exams").description("Exam and mock test management"),
                        new Tag().name("Questions").description("Question management for exams"),
                        new Tag().name("Purchases").description("Purchase and payment management"),
                        new Tag().name("Testimonials").description("User testimonials and success stories"),
                        new Tag().name("Pricing Plans").description("Subscription plans management"),
                        new Tag().name("Notifications").description("Notification system management"),
                        new Tag().name("Analytics").description("Dashboard analytics and statistics"),
                        new Tag().name("Admin Stats").description("Administrative statistics"),
                        new Tag().name("User Growth").description("User growth analytics"),
                        new Tag().name("Progress Data").description("User progress analytics"),
                        new Tag().name("Upcoming Exams").description("Upcoming exam information"),
                        new Tag().name("Recent Materials").description("Recently added materials")
                ));
    }
}
