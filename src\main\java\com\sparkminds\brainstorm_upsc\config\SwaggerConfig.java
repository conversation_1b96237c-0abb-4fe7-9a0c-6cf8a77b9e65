package com.sparkminds.brainstorm_upsc.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class SwaggerConfig {

        @Bean
        public OpenAPI customOpenAPI() {
                return new OpenAPI()
                                .info(new Info()
                                                .title("Brainstorm UPSC API")
                                                .description("Comprehensive REST API for Brainstorm UPSC platform")
                                                .version("1.0.0")
                                                .contact(new Contact()
                                                                .name("Brainstorm UPSC Team")
                                                                .email("<EMAIL>")))
                                .servers(List.of(
                                                new Server()
                                                                .url("http://localhost:8080/api")
                                                                .description("Development Server")));
        }
}
