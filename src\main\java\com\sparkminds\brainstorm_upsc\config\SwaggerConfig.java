package com.sparkminds.brainstorm_upsc.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

//@Configuration
public class SwaggerConfig {

        // @Bean
        public OpenAPI customOpenAPI() {
                return new OpenAPI()
                                .info(new Info()
                                                .title("Brainstorm UPSC API")
                                                .description("REST API for Brainstorm UPSC platform")
                                                .version("1.0.0"));
        }
}
