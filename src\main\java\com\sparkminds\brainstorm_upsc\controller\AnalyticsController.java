package com.sparkminds.brainstorm_upsc.controller;

import com.sparkminds.brainstorm_upsc.entity.*;
import com.sparkminds.brainstorm_upsc.service.AnalyticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@CrossOrigin(origins = "*")
// @io.swagger.v3.oas.annotations.tags.Tag(name = "Analytics", description =
// "Dashboard analytics and statistics")
public class AnalyticsController {

    @Autowired
    private AnalyticsService analyticsService;

    // Admin Stats endpoints
    @GetMapping("/adminStats")
    public ResponseEntity<List<AdminStats>> getAllAdminStats() {
        List<AdminStats> stats = analyticsService.getAllAdminStats();
        return ResponseEntity.ok(stats);
    }

    @GetMapping("/adminStats/{id}")
    public ResponseEntity<AdminStats> getAdminStatById(@PathVariable String id) {
        Optional<AdminStats> stat = analyticsService.getAdminStatById(id);
        return stat.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping("/adminStats")
    public ResponseEntity<AdminStats> createAdminStats(@RequestBody AdminStats adminStats) {
        AdminStats savedStats = analyticsService.saveAdminStats(adminStats);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedStats);
    }

    @PutMapping("/adminStats/{id}")
    public ResponseEntity<AdminStats> updateAdminStats(@PathVariable String id, @RequestBody AdminStats adminStats) {
        Optional<AdminStats> existingStats = analyticsService.getAdminStatById(id);
        if (existingStats.isPresent()) {
            adminStats.setId(id);
            AdminStats updatedStats = analyticsService.saveAdminStats(adminStats);
            return ResponseEntity.ok(updatedStats);
        }
        return ResponseEntity.notFound().build();
    }

    @DeleteMapping("/adminStats/{id}")
    public ResponseEntity<Void> deleteAdminStats(@PathVariable String id) {
        Optional<AdminStats> stats = analyticsService.getAdminStatById(id);
        if (stats.isPresent()) {
            analyticsService.deleteAdminStats(id);
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }

    // User Growth Data endpoints
    @GetMapping("/userGrowthData")
    public ResponseEntity<List<UserGrowthData>> getAllUserGrowthData() {
        List<UserGrowthData> data = analyticsService.getAllUserGrowthData();
        return ResponseEntity.ok(data);
    }

    @GetMapping("/userGrowthData/{id}")
    public ResponseEntity<UserGrowthData> getUserGrowthDataById(@PathVariable String id) {
        Optional<UserGrowthData> data = analyticsService.getUserGrowthDataById(id);
        return data.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping("/userGrowthData")
    public ResponseEntity<UserGrowthData> createUserGrowthData(@RequestBody UserGrowthData userGrowthData) {
        UserGrowthData savedData = analyticsService.saveUserGrowthData(userGrowthData);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedData);
    }

    @PutMapping("/userGrowthData/{id}")
    public ResponseEntity<UserGrowthData> updateUserGrowthData(@PathVariable String id,
            @RequestBody UserGrowthData userGrowthData) {
        Optional<UserGrowthData> existingData = analyticsService.getUserGrowthDataById(id);
        if (existingData.isPresent()) {
            userGrowthData.setId(id);
            UserGrowthData updatedData = analyticsService.saveUserGrowthData(userGrowthData);
            return ResponseEntity.ok(updatedData);
        }
        return ResponseEntity.notFound().build();
    }

    @DeleteMapping("/userGrowthData/{id}")
    public ResponseEntity<Void> deleteUserGrowthData(@PathVariable String id) {
        Optional<UserGrowthData> data = analyticsService.getUserGrowthDataById(id);
        if (data.isPresent()) {
            analyticsService.deleteUserGrowthData(id);
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }

    // Progress Data endpoints
    @GetMapping("/progressData")
    public ResponseEntity<List<ProgressData>> getAllProgressData() {
        List<ProgressData> data = analyticsService.getAllProgressData();
        return ResponseEntity.ok(data);
    }

    @GetMapping("/progressData/{id}")
    public ResponseEntity<ProgressData> getProgressDataById(@PathVariable String id) {
        Optional<ProgressData> data = analyticsService.getProgressDataById(id);
        return data.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping("/progressData")
    public ResponseEntity<ProgressData> createProgressData(@RequestBody ProgressData progressData) {
        ProgressData savedData = analyticsService.saveProgressData(progressData);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedData);
    }

    @PutMapping("/progressData/{id}")
    public ResponseEntity<ProgressData> updateProgressData(@PathVariable String id,
            @RequestBody ProgressData progressData) {
        Optional<ProgressData> existingData = analyticsService.getProgressDataById(id);
        if (existingData.isPresent()) {
            progressData.setId(id);
            ProgressData updatedData = analyticsService.saveProgressData(progressData);
            return ResponseEntity.ok(updatedData);
        }
        return ResponseEntity.notFound().build();
    }

    @DeleteMapping("/progressData/{id}")
    public ResponseEntity<Void> deleteProgressData(@PathVariable String id) {
        Optional<ProgressData> data = analyticsService.getProgressDataById(id);
        if (data.isPresent()) {
            analyticsService.deleteProgressData(id);
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }

    // Upcoming Exams endpoints
    @GetMapping("/upcomingExams")
    public ResponseEntity<List<UpcomingExam>> getAllUpcomingExams(
            @RequestParam(required = false) String q) {

        List<UpcomingExam> exams;

        if (q != null && !q.isEmpty()) {
            exams = analyticsService.searchUpcomingExams(q);
        } else {
            exams = analyticsService.getAllUpcomingExams();
        }

        return ResponseEntity.ok(exams);
    }

    @GetMapping("/upcomingExams/{id}")
    public ResponseEntity<UpcomingExam> getUpcomingExamById(@PathVariable String id) {
        Optional<UpcomingExam> exam = analyticsService.getUpcomingExamById(id);
        return exam.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping("/upcomingExams")
    public ResponseEntity<UpcomingExam> createUpcomingExam(@RequestBody UpcomingExam upcomingExam) {
        UpcomingExam savedExam = analyticsService.saveUpcomingExam(upcomingExam);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedExam);
    }

    @PutMapping("/upcomingExams/{id}")
    public ResponseEntity<UpcomingExam> updateUpcomingExam(@PathVariable String id,
            @RequestBody UpcomingExam upcomingExam) {
        Optional<UpcomingExam> existingExam = analyticsService.getUpcomingExamById(id);
        if (existingExam.isPresent()) {
            upcomingExam.setId(id);
            UpcomingExam updatedExam = analyticsService.saveUpcomingExam(upcomingExam);
            return ResponseEntity.ok(updatedExam);
        }
        return ResponseEntity.notFound().build();
    }

    @DeleteMapping("/upcomingExams/{id}")
    public ResponseEntity<Void> deleteUpcomingExam(@PathVariable String id) {
        Optional<UpcomingExam> exam = analyticsService.getUpcomingExamById(id);
        if (exam.isPresent()) {
            analyticsService.deleteUpcomingExam(id);
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }

    // Recent Materials endpoints
    @GetMapping("/recentMaterials")
    public ResponseEntity<List<RecentMaterial>> getAllRecentMaterials(
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String q) {

        List<RecentMaterial> materials;

        if (q != null && !q.isEmpty()) {
            materials = analyticsService.searchRecentMaterials(q);
        } else if (type != null) {
            materials = analyticsService.getRecentMaterialsByType(type);
        } else {
            materials = analyticsService.getAllRecentMaterials();
        }

        return ResponseEntity.ok(materials);
    }

    @GetMapping("/recentMaterials/{id}")
    public ResponseEntity<RecentMaterial> getRecentMaterialById(@PathVariable String id) {
        Optional<RecentMaterial> material = analyticsService.getRecentMaterialById(id);
        return material.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping("/recentMaterials")
    public ResponseEntity<RecentMaterial> createRecentMaterial(@RequestBody RecentMaterial recentMaterial) {
        RecentMaterial savedMaterial = analyticsService.saveRecentMaterial(recentMaterial);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedMaterial);
    }

    @PutMapping("/recentMaterials/{id}")
    public ResponseEntity<RecentMaterial> updateRecentMaterial(@PathVariable String id,
            @RequestBody RecentMaterial recentMaterial) {
        Optional<RecentMaterial> existingMaterial = analyticsService.getRecentMaterialById(id);
        if (existingMaterial.isPresent()) {
            recentMaterial.setId(id);
            RecentMaterial updatedMaterial = analyticsService.saveRecentMaterial(recentMaterial);
            return ResponseEntity.ok(updatedMaterial);
        }
        return ResponseEntity.notFound().build();
    }

    @DeleteMapping("/recentMaterials/{id}")
    public ResponseEntity<Void> deleteRecentMaterial(@PathVariable String id) {
        Optional<RecentMaterial> material = analyticsService.getRecentMaterialById(id);
        if (material.isPresent()) {
            analyticsService.deleteRecentMaterial(id);
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }
}
