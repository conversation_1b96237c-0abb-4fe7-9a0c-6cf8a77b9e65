package com.sparkminds.brainstorm_upsc.controller;

import com.sparkminds.brainstorm_upsc.entity.Exam;
import com.sparkminds.brainstorm_upsc.service.ExamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/exams")
@CrossOrigin(origins = "*")
public class ExamController {
    
    @Autowired
    private ExamService examService;
    
    // Get all exams with filtering
    @GetMapping
    public ResponseEntity<List<Exam>> getAllExams(
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String difficulty,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String q) {
        
        List<Exam> exams;
        
        // Handle search query
        if (q != null && !q.isEmpty()) {
            exams = examService.searchExams(q);
        } else if (type != null) {
            exams = examService.getExamsByType(type);
        } else if (difficulty != null) {
            exams = examService.getExamsByDifficulty(difficulty);
        } else if (status != null) {
            exams = "published".equals(status) ? examService.getPublishedExams() : examService.getAllExams();
        } else {
            exams = examService.getPublishedExams();
        }
        
        return ResponseEntity.ok(exams);
    }
    
    // Get exam by ID
    @GetMapping("/{id}")
    public ResponseEntity<Exam> getExamById(@PathVariable String id) {
        Optional<Exam> exam = examService.getExamById(id);
        return exam.map(ResponseEntity::ok)
                  .orElse(ResponseEntity.notFound().build());
    }
    
    // Create new exam
    @PostMapping
    public ResponseEntity<Exam> createExam(@RequestBody Exam exam) {
        Exam savedExam = examService.saveExam(exam);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedExam);
    }
    
    // Update exam
    @PutMapping("/{id}")
    public ResponseEntity<Exam> updateExam(@PathVariable String id, @RequestBody Exam exam) {
        Optional<Exam> existingExam = examService.getExamById(id);
        if (existingExam.isPresent()) {
            exam.setId(id);
            Exam updatedExam = examService.saveExam(exam);
            return ResponseEntity.ok(updatedExam);
        }
        return ResponseEntity.notFound().build();
    }
    
    // Partially update exam
    @PatchMapping("/{id}")
    public ResponseEntity<Exam> patchExam(@PathVariable String id, @RequestBody Exam examUpdates) {
        Optional<Exam> existingExamOpt = examService.getExamById(id);
        if (existingExamOpt.isPresent()) {
            Exam existingExam = existingExamOpt.get();
            
            // Update only non-null fields
            if (examUpdates.getTitle() != null) existingExam.setTitle(examUpdates.getTitle());
            if (examUpdates.getDescription() != null) existingExam.setDescription(examUpdates.getDescription());
            if (examUpdates.getType() != null) existingExam.setType(examUpdates.getType());
            if (examUpdates.getDuration() != null) existingExam.setDuration(examUpdates.getDuration());
            if (examUpdates.getQuestions() != null) existingExam.setQuestions(examUpdates.getQuestions());
            if (examUpdates.getDifficulty() != null) existingExam.setDifficulty(examUpdates.getDifficulty());
            if (examUpdates.getStatus() != null) existingExam.setStatus(examUpdates.getStatus());
            if (examUpdates.getStartDate() != null) existingExam.setStartDate(examUpdates.getStartDate());
            if (examUpdates.getStartTime() != null) existingExam.setStartTime(examUpdates.getStartTime());
            if (examUpdates.getScheduledAt() != null) existingExam.setScheduledAt(examUpdates.getScheduledAt());
            
            Exam updatedExam = examService.saveExam(existingExam);
            return ResponseEntity.ok(updatedExam);
        }
        return ResponseEntity.notFound().build();
    }
    
    // Delete exam
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteExam(@PathVariable String id) {
        Optional<Exam> exam = examService.getExamById(id);
        if (exam.isPresent()) {
            examService.deleteExam(id);
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }
    
    // Get upcoming exams
    @GetMapping("/upcoming")
    public ResponseEntity<List<Exam>> getUpcomingExams() {
        List<Exam> exams = examService.getUpcomingExams();
        return ResponseEntity.ok(exams);
    }
    
    // Get mock tests
    @GetMapping("/mock-tests")
    public ResponseEntity<List<Exam>> getMockTests() {
        List<Exam> exams = examService.getMockTests();
        return ResponseEntity.ok(exams);
    }
    
    // Get subject tests
    @GetMapping("/subject-tests")
    public ResponseEntity<List<Exam>> getSubjectTests() {
        List<Exam> exams = examService.getSubjectTests();
        return ResponseEntity.ok(exams);
    }
    
    // Get current affairs tests
    @GetMapping("/current-affairs")
    public ResponseEntity<List<Exam>> getCurrentAffairsTests() {
        List<Exam> exams = examService.getCurrentAffairsTests();
        return ResponseEntity.ok(exams);
    }
    
    // Get popular exams
    @GetMapping("/popular")
    public ResponseEntity<List<Exam>> getPopularExams(
            @RequestParam(defaultValue = "100") Integer minParticipants) {
        List<Exam> exams = examService.getPopularExams(minParticipants);
        return ResponseEntity.ok(exams);
    }
    
    // Get recent exams
    @GetMapping("/recent")
    public ResponseEntity<List<Exam>> getRecentExams() {
        List<Exam> exams = examService.getRecentExams();
        return ResponseEntity.ok(exams);
    }
    
    // Get today's exams
    @GetMapping("/today")
    public ResponseEntity<List<Exam>> getTodaysExams() {
        List<Exam> exams = examService.getTodaysExams();
        return ResponseEntity.ok(exams);
    }
    
    // Get this week's exams
    @GetMapping("/this-week")
    public ResponseEntity<List<Exam>> getThisWeeksExams() {
        List<Exam> exams = examService.getThisWeeksExams();
        return ResponseEntity.ok(exams);
    }
    
    // Join exam (increment participant count)
    @PostMapping("/{id}/join")
    public ResponseEntity<Exam> joinExam(@PathVariable String id) {
        Exam exam = examService.incrementParticipantCount(id);
        if (exam != null) {
            return ResponseEntity.ok(exam);
        }
        return ResponseEntity.notFound().build();
    }
    
    // Get exam statistics
    @GetMapping("/stats")
    public ResponseEntity<ExamStats> getExamStats() {
        ExamStats stats = new ExamStats();
        stats.setTotalExams(examService.getTotalExams());
        stats.setPublishedExamCount(examService.getPublishedExamCount());
        stats.setDraftExamCount(examService.getDraftExamCount());
        stats.setMockTestCount(examService.getMockTestCount());
        stats.setSubjectTestCount(examService.getSubjectTestCount());
        stats.setCurrentAffairsTestCount(examService.getCurrentAffairsTestCount());
        
        return ResponseEntity.ok(stats);
    }
    
    // Inner class for statistics
    public static class ExamStats {
        private long totalExams;
        private long publishedExamCount;
        private long draftExamCount;
        private long mockTestCount;
        private long subjectTestCount;
        private long currentAffairsTestCount;
        
        // Getters and setters
        public long getTotalExams() { return totalExams; }
        public void setTotalExams(long totalExams) { this.totalExams = totalExams; }
        public long getPublishedExamCount() { return publishedExamCount; }
        public void setPublishedExamCount(long publishedExamCount) { this.publishedExamCount = publishedExamCount; }
        public long getDraftExamCount() { return draftExamCount; }
        public void setDraftExamCount(long draftExamCount) { this.draftExamCount = draftExamCount; }
        public long getMockTestCount() { return mockTestCount; }
        public void setMockTestCount(long mockTestCount) { this.mockTestCount = mockTestCount; }
        public long getSubjectTestCount() { return subjectTestCount; }
        public void setSubjectTestCount(long subjectTestCount) { this.subjectTestCount = subjectTestCount; }
        public long getCurrentAffairsTestCount() { return currentAffairsTestCount; }
        public void setCurrentAffairsTestCount(long currentAffairsTestCount) { this.currentAffairsTestCount = currentAffairsTestCount; }
    }
}
