package com.sparkminds.brainstorm_upsc.controller;

import com.sparkminds.brainstorm_upsc.entity.Notification;
import com.sparkminds.brainstorm_upsc.service.NotificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/notifications")
@CrossOrigin(origins = "*")
// @io.swagger.v3.oas.annotations.tags.Tag(name = "Notifications", description =
// "Notification system management")
public class NotificationController {

    @Autowired
    private NotificationService notificationService;

    // Get all notifications with filtering
    @GetMapping
    public ResponseEntity<List<Notification>> getAllNotifications(
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String audience,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Boolean read) {

        List<Notification> notifications;

        if (type != null) {
            notifications = notificationService.getNotificationsByType(type);
        } else if (audience != null) {
            if (read != null && !read) {
                notifications = notificationService.getUnreadNotificationsForAudience(audience);
            } else {
                notifications = notificationService.getNotificationsByAudience(audience);
            }
        } else if (status != null) {
            notifications = notificationService.getNotificationsByStatus(status);
        } else {
            notifications = notificationService.getAllNotifications();
        }

        return ResponseEntity.ok(notifications);
    }

    // Get notification by ID
    @GetMapping("/{id}")
    public ResponseEntity<Notification> getNotificationById(@PathVariable String id) {
        Optional<Notification> notification = notificationService.getNotificationById(id);
        return notification.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    // Create new notification
    @PostMapping
    public ResponseEntity<Notification> createNotification(@RequestBody NotificationRequest request) {
        Notification notification = notificationService.createNotification(
                request.getTitle(),
                request.getMessage(),
                request.getType(),
                request.getAudience());
        return ResponseEntity.status(HttpStatus.CREATED).body(notification);
    }

    // Update notification
    @PutMapping("/{id}")
    public ResponseEntity<Notification> updateNotification(@PathVariable String id,
            @RequestBody Notification notification) {
        Optional<Notification> existingNotification = notificationService.getNotificationById(id);
        if (existingNotification.isPresent()) {
            notification.setId(id);
            Notification updatedNotification = notificationService.saveNotification(notification);
            return ResponseEntity.ok(updatedNotification);
        }
        return ResponseEntity.notFound().build();
    }

    // Partially update notification
    @PatchMapping("/{id}")
    public ResponseEntity<Notification> patchNotification(@PathVariable String id,
            @RequestBody Notification notificationUpdates) {
        Optional<Notification> existingNotificationOpt = notificationService.getNotificationById(id);
        if (existingNotificationOpt.isPresent()) {
            Notification existingNotification = existingNotificationOpt.get();

            // Update only non-null fields
            if (notificationUpdates.getTitle() != null)
                existingNotification.setTitle(notificationUpdates.getTitle());
            if (notificationUpdates.getMessage() != null)
                existingNotification.setMessage(notificationUpdates.getMessage());
            if (notificationUpdates.getType() != null)
                existingNotification.setType(notificationUpdates.getType());
            if (notificationUpdates.getRead() != null)
                existingNotification.setRead(notificationUpdates.getRead());
            if (notificationUpdates.getAudience() != null)
                existingNotification.setAudience(notificationUpdates.getAudience());
            if (notificationUpdates.getStatus() != null)
                existingNotification.setStatus(notificationUpdates.getStatus());
            if (notificationUpdates.getRecipients() != null)
                existingNotification.setRecipients(notificationUpdates.getRecipients());
            if (notificationUpdates.getOpenRate() != null)
                existingNotification.setOpenRate(notificationUpdates.getOpenRate());

            Notification updatedNotification = notificationService.saveNotification(existingNotification);
            return ResponseEntity.ok(updatedNotification);
        }
        return ResponseEntity.notFound().build();
    }

    // Delete notification
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteNotification(@PathVariable String id) {
        Optional<Notification> notification = notificationService.getNotificationById(id);
        if (notification.isPresent()) {
            notificationService.deleteNotification(id);
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }

    // Mark notification as read
    @PostMapping("/{id}/read")
    public ResponseEntity<Notification> markAsRead(@PathVariable String id) {
        Notification notification = notificationService.markAsRead(id);
        if (notification != null) {
            return ResponseEntity.ok(notification);
        }
        return ResponseEntity.notFound().build();
    }

    // Send notification
    @PostMapping("/{id}/send")
    public ResponseEntity<Notification> sendNotification(@PathVariable String id, @RequestBody SendRequest request) {
        Notification notification = notificationService.sendNotification(id, request.getRecipients());
        if (notification != null) {
            return ResponseEntity.ok(notification);
        }
        return ResponseEntity.notFound().build();
    }

    // Get recent notifications
    @GetMapping("/recent")
    public ResponseEntity<List<Notification>> getRecentNotifications() {
        List<Notification> notifications = notificationService.getRecentNotifications();
        return ResponseEntity.ok(notifications);
    }

    // Get unread notifications for audience
    @GetMapping("/unread/{audience}")
    public ResponseEntity<List<Notification>> getUnreadNotificationsForAudience(@PathVariable String audience) {
        List<Notification> notifications = notificationService.getUnreadNotificationsForAudience(audience);
        return ResponseEntity.ok(notifications);
    }

    // Get notification statistics
    @GetMapping("/stats")
    public ResponseEntity<NotificationStats> getNotificationStats() {
        NotificationStats stats = new NotificationStats();
        stats.setTotalNotifications(notificationService.getTotalNotifications());
        stats.setUnreadNotificationCount(notificationService.getUnreadNotificationCount());
        stats.setSentNotificationCount(notificationService.getSentNotificationCount());
        stats.setScheduledNotificationCount(notificationService.getScheduledNotificationCount());
        stats.setDraftNotificationCount(notificationService.getDraftNotificationCount());

        return ResponseEntity.ok(stats);
    }

    // Inner classes for request/response DTOs
    public static class NotificationRequest {
        private String title;
        private String message;
        private String type;
        private String audience;

        // Getters and setters
        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getAudience() {
            return audience;
        }

        public void setAudience(String audience) {
            this.audience = audience;
        }
    }

    public static class SendRequest {
        private Integer recipients;

        public Integer getRecipients() {
            return recipients;
        }

        public void setRecipients(Integer recipients) {
            this.recipients = recipients;
        }
    }

    public static class NotificationStats {
        private long totalNotifications;
        private long unreadNotificationCount;
        private long sentNotificationCount;
        private long scheduledNotificationCount;
        private long draftNotificationCount;

        // Getters and setters
        public long getTotalNotifications() {
            return totalNotifications;
        }

        public void setTotalNotifications(long totalNotifications) {
            this.totalNotifications = totalNotifications;
        }

        public long getUnreadNotificationCount() {
            return unreadNotificationCount;
        }

        public void setUnreadNotificationCount(long unreadNotificationCount) {
            this.unreadNotificationCount = unreadNotificationCount;
        }

        public long getSentNotificationCount() {
            return sentNotificationCount;
        }

        public void setSentNotificationCount(long sentNotificationCount) {
            this.sentNotificationCount = sentNotificationCount;
        }

        public long getScheduledNotificationCount() {
            return scheduledNotificationCount;
        }

        public void setScheduledNotificationCount(long scheduledNotificationCount) {
            this.scheduledNotificationCount = scheduledNotificationCount;
        }

        public long getDraftNotificationCount() {
            return draftNotificationCount;
        }

        public void setDraftNotificationCount(long draftNotificationCount) {
            this.draftNotificationCount = draftNotificationCount;
        }
    }
}
