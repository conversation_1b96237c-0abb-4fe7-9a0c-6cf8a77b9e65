package com.sparkminds.brainstorm_upsc.controller;

import com.sparkminds.brainstorm_upsc.entity.PricingPlan;
import com.sparkminds.brainstorm_upsc.service.PricingPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/pricingPlans")
@CrossOrigin(origins = "*")
// @io.swagger.v3.oas.annotations.tags.Tag(name = "Pricing Plans", description =
// "Subscription plans management")
public class PricingPlanController {

    @Autowired
    private PricingPlanService pricingPlanService;

    // Get all pricing plans with filtering
    @GetMapping
    public ResponseEntity<List<PricingPlan>> getAllPricingPlans(
            @RequestParam(required = false) Boolean active,
            @RequestParam(required = false) Boolean popular) {

        List<PricingPlan> plans;

        if (active != null) {
            plans = pricingPlanService.getActivePricingPlans();
        } else if (popular != null && popular) {
            Optional<PricingPlan> popularPlan = pricingPlanService.getPopularPricingPlan();
            plans = popularPlan.map(List::of).orElse(List.of());
        } else {
            plans = pricingPlanService.getAllPricingPlans();
        }

        return ResponseEntity.ok(plans);
    }

    // Get pricing plan by ID
    @GetMapping("/{id}")
    public ResponseEntity<PricingPlan> getPricingPlanById(@PathVariable String id) {
        Optional<PricingPlan> plan = pricingPlanService.getPricingPlanById(id);
        return plan.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    // Create new pricing plan
    @PostMapping
    public ResponseEntity<PricingPlan> createPricingPlan(@RequestBody PricingPlan plan) {
        PricingPlan savedPlan = pricingPlanService.savePricingPlan(plan);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedPlan);
    }

    // Update pricing plan
    @PutMapping("/{id}")
    public ResponseEntity<PricingPlan> updatePricingPlan(@PathVariable String id, @RequestBody PricingPlan plan) {
        Optional<PricingPlan> existingPlan = pricingPlanService.getPricingPlanById(id);
        if (existingPlan.isPresent()) {
            plan.setId(id);
            PricingPlan updatedPlan = pricingPlanService.savePricingPlan(plan);
            return ResponseEntity.ok(updatedPlan);
        }
        return ResponseEntity.notFound().build();
    }

    // Partially update pricing plan
    @PatchMapping("/{id}")
    public ResponseEntity<PricingPlan> patchPricingPlan(@PathVariable String id, @RequestBody PricingPlan planUpdates) {
        Optional<PricingPlan> existingPlanOpt = pricingPlanService.getPricingPlanById(id);
        if (existingPlanOpt.isPresent()) {
            PricingPlan existingPlan = existingPlanOpt.get();

            // Update only non-null fields
            if (planUpdates.getName() != null)
                existingPlan.setName(planUpdates.getName());
            if (planUpdates.getPrice() != null)
                existingPlan.setPrice(planUpdates.getPrice());
            if (planUpdates.getPeriod() != null)
                existingPlan.setPeriod(planUpdates.getPeriod());
            if (planUpdates.getOriginalPrice() != null)
                existingPlan.setOriginalPrice(planUpdates.getOriginalPrice());
            if (planUpdates.getFeatures() != null)
                existingPlan.setFeatures(planUpdates.getFeatures());
            if (planUpdates.getPopular() != null)
                existingPlan.setPopular(planUpdates.getPopular());
            if (planUpdates.getActive() != null)
                existingPlan.setActive(planUpdates.getActive());

            PricingPlan updatedPlan = pricingPlanService.savePricingPlan(existingPlan);
            return ResponseEntity.ok(updatedPlan);
        }
        return ResponseEntity.notFound().build();
    }

    // Delete pricing plan
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletePricingPlan(@PathVariable String id) {
        Optional<PricingPlan> plan = pricingPlanService.getPricingPlanById(id);
        if (plan.isPresent()) {
            pricingPlanService.deletePricingPlan(id);
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }

    // Get active pricing plans
    @GetMapping("/active")
    public ResponseEntity<List<PricingPlan>> getActivePricingPlans() {
        List<PricingPlan> plans = pricingPlanService.getActivePricingPlans();
        return ResponseEntity.ok(plans);
    }

    // Get popular pricing plan
    @GetMapping("/popular")
    public ResponseEntity<PricingPlan> getPopularPricingPlan() {
        Optional<PricingPlan> plan = pricingPlanService.getPopularPricingPlan();
        return plan.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    // Get pricing plan by name
    @GetMapping("/name/{name}")
    public ResponseEntity<PricingPlan> getPricingPlanByName(@PathVariable String name) {
        Optional<PricingPlan> plan = pricingPlanService.getPricingPlanByName(name);
        return plan.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    // Update active status
    @PostMapping("/{id}/active")
    public ResponseEntity<PricingPlan> updateActiveStatus(@PathVariable String id, @RequestBody ActiveRequest request) {
        PricingPlan plan = pricingPlanService.updatePricingPlanStatus(id, request.getActive());
        if (plan != null) {
            return ResponseEntity.ok(plan);
        }
        return ResponseEntity.notFound().build();
    }

    // Update popular status
    @PostMapping("/{id}/popular")
    public ResponseEntity<PricingPlan> updatePopularStatus(@PathVariable String id,
            @RequestBody PopularRequest request) {
        PricingPlan plan = pricingPlanService.updatePopularStatus(id, request.getPopular());
        if (plan != null) {
            return ResponseEntity.ok(plan);
        }
        return ResponseEntity.notFound().build();
    }

    // Get pricing plan statistics
    @GetMapping("/stats")
    public ResponseEntity<PricingPlanStats> getPricingPlanStats() {
        PricingPlanStats stats = new PricingPlanStats();
        stats.setTotalPricingPlans(pricingPlanService.getTotalPricingPlans());
        stats.setActivePricingPlanCount(pricingPlanService.getActivePricingPlanCount());

        return ResponseEntity.ok(stats);
    }

    // Inner classes for request/response DTOs
    public static class ActiveRequest {
        private Boolean active;

        public Boolean getActive() {
            return active;
        }

        public void setActive(Boolean active) {
            this.active = active;
        }
    }

    public static class PopularRequest {
        private Boolean popular;

        public Boolean getPopular() {
            return popular;
        }

        public void setPopular(Boolean popular) {
            this.popular = popular;
        }
    }

    public static class PricingPlanStats {
        private long totalPricingPlans;
        private long activePricingPlanCount;

        public long getTotalPricingPlans() {
            return totalPricingPlans;
        }

        public void setTotalPricingPlans(long totalPricingPlans) {
            this.totalPricingPlans = totalPricingPlans;
        }

        public long getActivePricingPlanCount() {
            return activePricingPlanCount;
        }

        public void setActivePricingPlanCount(long activePricingPlanCount) {
            this.activePricingPlanCount = activePricingPlanCount;
        }
    }
}
