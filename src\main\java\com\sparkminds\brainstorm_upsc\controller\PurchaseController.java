package com.sparkminds.brainstorm_upsc.controller;

import com.sparkminds.brainstorm_upsc.entity.Purchase;
import com.sparkminds.brainstorm_upsc.service.PurchaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/purchases")
@CrossOrigin(origins = "*")
@io.swagger.v3.oas.annotations.tags.Tag(name = "Purchases", description = "Purchase and payment management")
public class PurchaseController {

    @Autowired
    private PurchaseService purchaseService;

    // Get all purchases with filtering
    @GetMapping
    public ResponseEntity<List<Purchase>> getAllPurchases(
            @RequestParam(required = false) String userId,
            @RequestParam(required = false) String materialId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String paymentMethod,
            @RequestParam(required = false) Boolean accessGranted) {

        List<Purchase> purchases;

        if (userId != null && materialId != null) {
            purchases = purchaseService.getPurchasesByUserId(userId)
                    .stream()
                    .filter(p -> materialId.equals(p.getMaterialId()))
                    .toList();
        } else if (userId != null) {
            if (status != null) {
                purchases = "completed".equals(status) ? purchaseService.getCompletedPurchasesByUser(userId)
                        : purchaseService.getPurchasesByUserId(userId)
                                .stream()
                                .filter(p -> status.equals(p.getStatus()))
                                .toList();
            } else {
                purchases = purchaseService.getPurchasesByUserId(userId);
            }
        } else if (materialId != null) {
            purchases = purchaseService.getPurchasesByMaterialId(materialId);
        } else if (status != null) {
            purchases = purchaseService.getPurchasesByStatus(status);
        } else if (paymentMethod != null) {
            purchases = purchaseService.getPurchasesByPaymentMethod(paymentMethod);
        } else if (accessGranted != null) {
            purchases = purchaseService.getAllPurchases()
                    .stream()
                    .filter(p -> accessGranted.equals(p.getAccessGranted()))
                    .toList();
        } else {
            purchases = purchaseService.getAllPurchases();
        }

        return ResponseEntity.ok(purchases);
    }

    // Get purchase by ID
    @GetMapping("/{id}")
    public ResponseEntity<Purchase> getPurchaseById(@PathVariable String id) {
        Optional<Purchase> purchase = purchaseService.getPurchaseById(id);
        return purchase.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    // Create new purchase
    @PostMapping
    public ResponseEntity<Purchase> createPurchase(@RequestBody PurchaseRequest request) {
        Purchase purchase = purchaseService.createPurchase(
                request.getUserId(),
                request.getMaterialId(),
                request.getAmount(),
                request.getPaymentMethod());
        return ResponseEntity.status(HttpStatus.CREATED).body(purchase);
    }

    // Update purchase
    @PutMapping("/{id}")
    public ResponseEntity<Purchase> updatePurchase(@PathVariable String id, @RequestBody Purchase purchase) {
        Optional<Purchase> existingPurchase = purchaseService.getPurchaseById(id);
        if (existingPurchase.isPresent()) {
            purchase.setId(id);
            Purchase updatedPurchase = purchaseService.savePurchase(purchase);
            return ResponseEntity.ok(updatedPurchase);
        }
        return ResponseEntity.notFound().build();
    }

    // Partially update purchase
    @PatchMapping("/{id}")
    public ResponseEntity<Purchase> patchPurchase(@PathVariable String id, @RequestBody Purchase purchaseUpdates) {
        Optional<Purchase> existingPurchaseOpt = purchaseService.getPurchaseById(id);
        if (existingPurchaseOpt.isPresent()) {
            Purchase existingPurchase = existingPurchaseOpt.get();

            // Update only non-null fields
            if (purchaseUpdates.getPaymentId() != null)
                existingPurchase.setPaymentId(purchaseUpdates.getPaymentId());
            if (purchaseUpdates.getOrderId() != null)
                existingPurchase.setOrderId(purchaseUpdates.getOrderId());
            if (purchaseUpdates.getStatus() != null)
                existingPurchase.setStatus(purchaseUpdates.getStatus());
            if (purchaseUpdates.getAccessGranted() != null)
                existingPurchase.setAccessGranted(purchaseUpdates.getAccessGranted());
            if (purchaseUpdates.getDownloadCount() != null)
                existingPurchase.setDownloadCount(purchaseUpdates.getDownloadCount());

            if ("completed".equals(purchaseUpdates.getStatus())) {
                existingPurchase.setAccessGranted(true);
                existingPurchase.setLastAccessedAt(LocalDateTime.now());
            }

            Purchase updatedPurchase = purchaseService.savePurchase(existingPurchase);
            return ResponseEntity.ok(updatedPurchase);
        }
        return ResponseEntity.notFound().build();
    }

    // Delete purchase
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletePurchase(@PathVariable String id) {
        Optional<Purchase> purchase = purchaseService.getPurchaseById(id);
        if (purchase.isPresent()) {
            purchaseService.deletePurchase(id);
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }

    // Complete purchase
    @PostMapping("/{id}/complete")
    public ResponseEntity<Purchase> completePurchase(@PathVariable String id, @RequestBody CompleteRequest request) {
        Purchase purchase = purchaseService.completePurchase(id, request.getPaymentId(), request.getOrderId());
        if (purchase != null) {
            return ResponseEntity.ok(purchase);
        }
        return ResponseEntity.notFound().build();
    }

    // Grant access
    @PostMapping("/{id}/grant-access")
    public ResponseEntity<Purchase> grantAccess(@PathVariable String id) {
        Purchase purchase = purchaseService.grantAccess(id);
        if (purchase != null) {
            return ResponseEntity.ok(purchase);
        }
        return ResponseEntity.notFound().build();
    }

    // Increment download count
    @PostMapping("/{id}/download")
    public ResponseEntity<Purchase> incrementDownloadCount(@PathVariable String id) {
        Purchase purchase = purchaseService.incrementDownloadCount(id);
        if (purchase != null) {
            return ResponseEntity.ok(purchase);
        }
        return ResponseEntity.notFound().build();
    }

    // Check if user has purchased material
    @GetMapping("/check")
    public ResponseEntity<PurchaseCheck> checkPurchase(
            @RequestParam String userId,
            @RequestParam String materialId) {
        boolean hasPurchased = purchaseService.hasUserPurchasedMaterial(userId, materialId);
        return ResponseEntity.ok(new PurchaseCheck(hasPurchased));
    }

    // Get purchase by payment ID
    @GetMapping("/payment/{paymentId}")
    public ResponseEntity<Purchase> getPurchaseByPaymentId(@PathVariable String paymentId) {
        Optional<Purchase> purchase = purchaseService.getPurchaseByPaymentId(paymentId);
        return purchase.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    // Get purchase by order ID
    @GetMapping("/order/{orderId}")
    public ResponseEntity<Purchase> getPurchaseByOrderId(@PathVariable String orderId) {
        Optional<Purchase> purchase = purchaseService.getPurchaseByOrderId(orderId);
        return purchase.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    // Get recent purchases
    @GetMapping("/recent")
    public ResponseEntity<List<Purchase>> getRecentPurchases() {
        List<Purchase> purchases = purchaseService.getRecentPurchases();
        return ResponseEntity.ok(purchases);
    }

    // Get purchase statistics
    @GetMapping("/stats")
    public ResponseEntity<PurchaseStats> getPurchaseStats() {
        PurchaseStats stats = new PurchaseStats();
        stats.setTotalPurchases(purchaseService.getTotalPurchases());
        stats.setCompletedPurchaseCount(purchaseService.getCompletedPurchaseCount());
        stats.setPendingPurchaseCount(purchaseService.getPendingPurchaseCount());
        stats.setFailedPurchaseCount(purchaseService.getFailedPurchaseCount());
        stats.setTotalRevenue(purchaseService.calculateTotalRevenue());

        return ResponseEntity.ok(stats);
    }

    // Inner classes for request/response DTOs
    public static class PurchaseRequest {
        private String userId;
        private String materialId;
        private Double amount;
        private String paymentMethod;

        // Getters and setters
        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getMaterialId() {
            return materialId;
        }

        public void setMaterialId(String materialId) {
            this.materialId = materialId;
        }

        public Double getAmount() {
            return amount;
        }

        public void setAmount(Double amount) {
            this.amount = amount;
        }

        public String getPaymentMethod() {
            return paymentMethod;
        }

        public void setPaymentMethod(String paymentMethod) {
            this.paymentMethod = paymentMethod;
        }
    }

    public static class CompleteRequest {
        private String paymentId;
        private String orderId;

        // Getters and setters
        public String getPaymentId() {
            return paymentId;
        }

        public void setPaymentId(String paymentId) {
            this.paymentId = paymentId;
        }

        public String getOrderId() {
            return orderId;
        }

        public void setOrderId(String orderId) {
            this.orderId = orderId;
        }
    }

    public static class PurchaseCheck {
        private boolean hasPurchased;

        public PurchaseCheck(boolean hasPurchased) {
            this.hasPurchased = hasPurchased;
        }

        public boolean isHasPurchased() {
            return hasPurchased;
        }

        public void setHasPurchased(boolean hasPurchased) {
            this.hasPurchased = hasPurchased;
        }
    }

    public static class PurchaseStats {
        private long totalPurchases;
        private long completedPurchaseCount;
        private long pendingPurchaseCount;
        private long failedPurchaseCount;
        private Double totalRevenue;

        // Getters and setters
        public long getTotalPurchases() {
            return totalPurchases;
        }

        public void setTotalPurchases(long totalPurchases) {
            this.totalPurchases = totalPurchases;
        }

        public long getCompletedPurchaseCount() {
            return completedPurchaseCount;
        }

        public void setCompletedPurchaseCount(long completedPurchaseCount) {
            this.completedPurchaseCount = completedPurchaseCount;
        }

        public long getPendingPurchaseCount() {
            return pendingPurchaseCount;
        }

        public void setPendingPurchaseCount(long pendingPurchaseCount) {
            this.pendingPurchaseCount = pendingPurchaseCount;
        }

        public long getFailedPurchaseCount() {
            return failedPurchaseCount;
        }

        public void setFailedPurchaseCount(long failedPurchaseCount) {
            this.failedPurchaseCount = failedPurchaseCount;
        }

        public Double getTotalRevenue() {
            return totalRevenue;
        }

        public void setTotalRevenue(Double totalRevenue) {
            this.totalRevenue = totalRevenue;
        }
    }
}
