package com.sparkminds.brainstorm_upsc.controller;

import com.sparkminds.brainstorm_upsc.entity.Question;
import com.sparkminds.brainstorm_upsc.service.QuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/questions")
@CrossOrigin(origins = "*")
// @io.swagger.v3.oas.annotations.tags.Tag(name = "Questions", description =
// "Question management for exams")
public class QuestionController {

    @Autowired
    private QuestionService questionService;

    // Get all questions with filtering
    @GetMapping
    public ResponseEntity<List<Question>> getAllQuestions(
            @RequestParam(required = false) String examId,
            @RequestParam(required = false) String q) {

        List<Question> questions;

        if (q != null && !q.isEmpty()) {
            questions = questionService.searchQuestions(q);
        } else if (examId != null) {
            questions = questionService.getQuestionsByExamId(examId);
        } else {
            questions = questionService.getAllQuestions();
        }

        return ResponseEntity.ok(questions);
    }

    // Get question by ID
    @GetMapping("/{id}")
    public ResponseEntity<Question> getQuestionById(@PathVariable String id) {
        Optional<Question> question = questionService.getQuestionById(id);
        return question.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    // Create new question
    @PostMapping
    public ResponseEntity<Question> createQuestion(@RequestBody Question question) {
        Question savedQuestion = questionService.saveQuestion(question);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedQuestion);
    }

    // Create multiple questions
    @PostMapping("/batch")
    public ResponseEntity<List<Question>> createQuestions(@RequestBody List<Question> questions) {
        List<Question> savedQuestions = questionService.saveAllQuestions(questions);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedQuestions);
    }

    // Update question
    @PutMapping("/{id}")
    public ResponseEntity<Question> updateQuestion(@PathVariable String id, @RequestBody Question question) {
        Optional<Question> existingQuestion = questionService.getQuestionById(id);
        if (existingQuestion.isPresent()) {
            question.setId(id);
            Question updatedQuestion = questionService.saveQuestion(question);
            return ResponseEntity.ok(updatedQuestion);
        }
        return ResponseEntity.notFound().build();
    }

    // Partially update question
    @PatchMapping("/{id}")
    public ResponseEntity<Question> patchQuestion(@PathVariable String id, @RequestBody Question questionUpdates) {
        Optional<Question> existingQuestionOpt = questionService.getQuestionById(id);
        if (existingQuestionOpt.isPresent()) {
            Question existingQuestion = existingQuestionOpt.get();

            // Update only non-null fields
            if (questionUpdates.getExamId() != null)
                existingQuestion.setExamId(questionUpdates.getExamId());
            if (questionUpdates.getQuestion() != null)
                existingQuestion.setQuestion(questionUpdates.getQuestion());
            if (questionUpdates.getOptions() != null)
                existingQuestion.setOptions(questionUpdates.getOptions());
            if (questionUpdates.getCorrect() != null)
                existingQuestion.setCorrect(questionUpdates.getCorrect());
            if (questionUpdates.getExplanation() != null)
                existingQuestion.setExplanation(questionUpdates.getExplanation());

            Question updatedQuestion = questionService.saveQuestion(existingQuestion);
            return ResponseEntity.ok(updatedQuestion);
        }
        return ResponseEntity.notFound().build();
    }

    // Delete question
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteQuestion(@PathVariable String id) {
        Optional<Question> question = questionService.getQuestionById(id);
        if (question.isPresent()) {
            questionService.deleteQuestion(id);
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }

    // Delete all questions for an exam
    @DeleteMapping("/exam/{examId}")
    public ResponseEntity<Void> deleteQuestionsByExamId(@PathVariable String examId) {
        questionService.deleteQuestionsByExamId(examId);
        return ResponseEntity.noContent().build();
    }

    // Get questions by exam ID
    @GetMapping("/exam/{examId}")
    public ResponseEntity<List<Question>> getQuestionsByExamId(@PathVariable String examId) {
        List<Question> questions = questionService.getQuestionsByExamId(examId);
        return ResponseEntity.ok(questions);
    }

    // Get question count for exam
    @GetMapping("/exam/{examId}/count")
    public ResponseEntity<QuestionCount> getQuestionCountByExam(@PathVariable String examId) {
        long count = questionService.getQuestionCountByExam(examId);
        return ResponseEntity.ok(new QuestionCount(count));
    }

    // Get questions by explanation containing text
    @GetMapping("/search/explanation")
    public ResponseEntity<List<Question>> getQuestionsByExplanation(@RequestParam String text) {
        List<Question> questions = questionService.getQuestionsByExplanation(text);
        return ResponseEntity.ok(questions);
    }

    // Get questions by correct answer index
    @GetMapping("/search/correct/{correctIndex}")
    public ResponseEntity<List<Question>> getQuestionsByCorrectAnswer(@PathVariable Integer correctIndex) {
        List<Question> questions = questionService.getQuestionsByCorrectAnswer(correctIndex);
        return ResponseEntity.ok(questions);
    }

    // Get question statistics
    @GetMapping("/stats")
    public ResponseEntity<QuestionStats> getQuestionStats() {
        QuestionStats stats = new QuestionStats();
        stats.setTotalQuestions(questionService.getTotalQuestions());
        return ResponseEntity.ok(stats);
    }

    // Inner classes for response DTOs
    public static class QuestionCount {
        private long count;

        public QuestionCount(long count) {
            this.count = count;
        }

        public long getCount() {
            return count;
        }

        public void setCount(long count) {
            this.count = count;
        }
    }

    public static class QuestionStats {
        private long totalQuestions;

        public long getTotalQuestions() {
            return totalQuestions;
        }

        public void setTotalQuestions(long totalQuestions) {
            this.totalQuestions = totalQuestions;
        }
    }
}
