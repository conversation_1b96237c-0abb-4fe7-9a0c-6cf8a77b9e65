package com.sparkminds.brainstorm_upsc.controller;

import com.sparkminds.brainstorm_upsc.entity.StudyMaterial;
import com.sparkminds.brainstorm_upsc.service.StudyMaterialService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/studyMaterials")
@CrossOrigin(origins = "*")
@Tag(name = "Study Materials", description = "Study materials management (PDFs, Videos, Audio)")
public class StudyMaterialController {

    @Autowired
    private StudyMaterialService studyMaterialService;

    // Get all study materials with filtering and pagination
    @GetMapping
    public ResponseEntity<?> getAllStudyMaterials(
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String subject,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Boolean isPremium,
            @RequestParam(required = false) String q,
            @RequestParam(required = false) Integer _page,
            @RequestParam(required = false) Integer _limit,
            @RequestParam(required = false) String _sort,
            @RequestParam(required = false) String _order) {

        // Handle search query
        if (q != null && !q.isEmpty()) {
            List<StudyMaterial> materials = studyMaterialService.searchMaterials(q);
            return ResponseEntity.ok(materials);
        }

        // Handle pagination
        if (_page != null && _limit != null) {
            Sort sort = Sort.unsorted();
            if (_sort != null) {
                Sort.Direction direction = "desc".equalsIgnoreCase(_order) ? Sort.Direction.DESC : Sort.Direction.ASC;
                sort = Sort.by(direction, _sort);
            }

            Pageable pageable = PageRequest.of(_page - 1, _limit, sort);
            Page<StudyMaterial> materialsPage;

            if (type != null) {
                materialsPage = studyMaterialService.getMaterialsByTypeWithPagination(type, pageable);
            } else if (subject != null) {
                materialsPage = studyMaterialService.getMaterialsBySubjectWithPagination(subject, pageable);
            } else {
                materialsPage = studyMaterialService.getMaterialsWithPagination(pageable);
            }

            return ResponseEntity.ok(materialsPage.getContent());
        }

        // Handle filtering without pagination
        List<StudyMaterial> materials;

        if (type != null) {
            materials = studyMaterialService.getMaterialsByType(type);
        } else if (subject != null) {
            materials = studyMaterialService.getMaterialsBySubject(subject);
        } else if (isPremium != null) {
            materials = isPremium ? studyMaterialService.getPremiumMaterials()
                    : studyMaterialService.getFreeMaterials();
        } else if (status != null) {
            materials = "published".equals(status) ? studyMaterialService.getPublishedMaterials()
                    : studyMaterialService.getAllMaterials();
        } else {
            materials = studyMaterialService.getPublishedMaterials();
        }

        return ResponseEntity.ok(materials);
    }

    // Get study material by ID
    @GetMapping("/{id}")
    public ResponseEntity<StudyMaterial> getStudyMaterialById(@PathVariable String id) {
        Optional<StudyMaterial> material = studyMaterialService.getMaterialById(id);
        return material.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    // Create new study material
    @PostMapping
    public ResponseEntity<StudyMaterial> createStudyMaterial(@RequestBody StudyMaterial material) {
        StudyMaterial savedMaterial = studyMaterialService.saveMaterial(material);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedMaterial);
    }

    // Update study material
    @PutMapping("/{id}")
    public ResponseEntity<StudyMaterial> updateStudyMaterial(@PathVariable String id,
            @RequestBody StudyMaterial material) {
        Optional<StudyMaterial> existingMaterial = studyMaterialService.getMaterialById(id);
        if (existingMaterial.isPresent()) {
            material.setId(id);
            StudyMaterial updatedMaterial = studyMaterialService.saveMaterial(material);
            return ResponseEntity.ok(updatedMaterial);
        }
        return ResponseEntity.notFound().build();
    }

    // Partially update study material
    @PatchMapping("/{id}")
    public ResponseEntity<StudyMaterial> patchStudyMaterial(@PathVariable String id,
            @RequestBody StudyMaterial materialUpdates) {
        Optional<StudyMaterial> existingMaterialOpt = studyMaterialService.getMaterialById(id);
        if (existingMaterialOpt.isPresent()) {
            StudyMaterial existingMaterial = existingMaterialOpt.get();

            // Update only non-null fields
            if (materialUpdates.getTitle() != null)
                existingMaterial.setTitle(materialUpdates.getTitle());
            if (materialUpdates.getDescription() != null)
                existingMaterial.setDescription(materialUpdates.getDescription());
            if (materialUpdates.getType() != null)
                existingMaterial.setType(materialUpdates.getType());
            if (materialUpdates.getSubject() != null)
                existingMaterial.setSubject(materialUpdates.getSubject());
            if (materialUpdates.getPrice() != null)
                existingMaterial.setPrice(materialUpdates.getPrice());
            if (materialUpdates.getOriginalPrice() != null)
                existingMaterial.setOriginalPrice(materialUpdates.getOriginalPrice());
            if (materialUpdates.getRating() != null)
                existingMaterial.setRating(materialUpdates.getRating());
            if (materialUpdates.getReviews() != null)
                existingMaterial.setReviews(materialUpdates.getReviews());
            if (materialUpdates.getAuthor() != null)
                existingMaterial.setAuthor(materialUpdates.getAuthor());
            if (materialUpdates.getIsPremium() != null)
                existingMaterial.setIsPremium(materialUpdates.getIsPremium());
            if (materialUpdates.getStatus() != null)
                existingMaterial.setStatus(materialUpdates.getStatus());
            if (materialUpdates.getThumbnailUrl() != null)
                existingMaterial.setThumbnailUrl(materialUpdates.getThumbnailUrl());

            StudyMaterial updatedMaterial = studyMaterialService.saveMaterial(existingMaterial);
            return ResponseEntity.ok(updatedMaterial);
        }
        return ResponseEntity.notFound().build();
    }

    // Delete study material
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteStudyMaterial(@PathVariable String id) {
        Optional<StudyMaterial> material = studyMaterialService.getMaterialById(id);
        if (material.isPresent()) {
            studyMaterialService.deleteMaterial(id);
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }

    // Get popular materials
    @GetMapping("/popular")
    public ResponseEntity<List<StudyMaterial>> getPopularMaterials(
            @RequestParam(defaultValue = "4.0") Double minRating,
            @RequestParam(defaultValue = "50") Integer minReviews) {
        List<StudyMaterial> materials = studyMaterialService.getPopularMaterials(minRating, minReviews);
        return ResponseEntity.ok(materials);
    }

    // Get top rated materials
    @GetMapping("/top-rated")
    public ResponseEntity<List<StudyMaterial>> getTopRatedMaterials() {
        List<StudyMaterial> materials = studyMaterialService.getTopRatedMaterials();
        return ResponseEntity.ok(materials);
    }

    // Get most downloaded materials
    @GetMapping("/most-downloaded")
    public ResponseEntity<List<StudyMaterial>> getMostDownloadedMaterials() {
        List<StudyMaterial> materials = studyMaterialService.getMostDownloadedMaterials();
        return ResponseEntity.ok(materials);
    }

    // Get recent materials
    @GetMapping("/recent")
    public ResponseEntity<List<StudyMaterial>> getRecentMaterials() {
        List<StudyMaterial> materials = studyMaterialService.getRecentMaterials();
        return ResponseEntity.ok(materials);
    }

    // Increment download count
    @PostMapping("/{id}/download")
    public ResponseEntity<StudyMaterial> incrementDownloadCount(@PathVariable String id) {
        StudyMaterial material = studyMaterialService.incrementDownloadCount(id);
        if (material != null) {
            return ResponseEntity.ok(material);
        }
        return ResponseEntity.notFound().build();
    }

    // Get material statistics
    @GetMapping("/stats")
    public ResponseEntity<MaterialStats> getMaterialStats() {
        MaterialStats stats = new MaterialStats();
        stats.setTotalMaterials(studyMaterialService.getTotalMaterials());
        stats.setPublishedMaterialCount(studyMaterialService.getPublishedMaterialCount());
        stats.setDraftMaterialCount(studyMaterialService.getDraftMaterialCount());
        stats.setPremiumMaterialCount(studyMaterialService.getPremiumMaterialCount());
        stats.setFreeMaterialCount(studyMaterialService.getFreeMaterialCount());

        return ResponseEntity.ok(stats);
    }

    // Inner class for statistics
    public static class MaterialStats {
        private long totalMaterials;
        private long publishedMaterialCount;
        private long draftMaterialCount;
        private long premiumMaterialCount;
        private long freeMaterialCount;

        // Getters and setters
        public long getTotalMaterials() {
            return totalMaterials;
        }

        public void setTotalMaterials(long totalMaterials) {
            this.totalMaterials = totalMaterials;
        }

        public long getPublishedMaterialCount() {
            return publishedMaterialCount;
        }

        public void setPublishedMaterialCount(long publishedMaterialCount) {
            this.publishedMaterialCount = publishedMaterialCount;
        }

        public long getDraftMaterialCount() {
            return draftMaterialCount;
        }

        public void setDraftMaterialCount(long draftMaterialCount) {
            this.draftMaterialCount = draftMaterialCount;
        }

        public long getPremiumMaterialCount() {
            return premiumMaterialCount;
        }

        public void setPremiumMaterialCount(long premiumMaterialCount) {
            this.premiumMaterialCount = premiumMaterialCount;
        }

        public long getFreeMaterialCount() {
            return freeMaterialCount;
        }

        public void setFreeMaterialCount(long freeMaterialCount) {
            this.freeMaterialCount = freeMaterialCount;
        }
    }
}
