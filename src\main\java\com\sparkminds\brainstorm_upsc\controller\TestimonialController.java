package com.sparkminds.brainstorm_upsc.controller;

import com.sparkminds.brainstorm_upsc.entity.Testimonial;
import com.sparkminds.brainstorm_upsc.service.TestimonialService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/testimonials")
@CrossOrigin(origins = "*")
public class TestimonialController {
    
    @Autowired
    private TestimonialService testimonialService;
    
    // Get all testimonials with filtering
    @GetMapping
    public ResponseEntity<List<Testimonial>> getAllTestimonials(
            @RequestParam(required = false) Boolean featured,
            @RequestParam(required = false) String q) {
        
        List<Testimonial> testimonials;
        
        if (q != null && !q.isEmpty()) {
            testimonials = testimonialService.searchTestimonialsByName(q);
        } else if (featured != null) {
            testimonials = testimonialService.getFeaturedTestimonials();
        } else {
            testimonials = testimonialService.getAllTestimonials();
        }
        
        return ResponseEntity.ok(testimonials);
    }
    
    // Get testimonial by ID
    @GetMapping("/{id}")
    public ResponseEntity<Testimonial> getTestimonialById(@PathVariable String id) {
        Optional<Testimonial> testimonial = testimonialService.getTestimonialById(id);
        return testimonial.map(ResponseEntity::ok)
                         .orElse(ResponseEntity.notFound().build());
    }
    
    // Create new testimonial
    @PostMapping
    public ResponseEntity<Testimonial> createTestimonial(@RequestBody Testimonial testimonial) {
        Testimonial savedTestimonial = testimonialService.saveTestimonial(testimonial);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedTestimonial);
    }
    
    // Update testimonial
    @PutMapping("/{id}")
    public ResponseEntity<Testimonial> updateTestimonial(@PathVariable String id, @RequestBody Testimonial testimonial) {
        Optional<Testimonial> existingTestimonial = testimonialService.getTestimonialById(id);
        if (existingTestimonial.isPresent()) {
            testimonial.setId(id);
            Testimonial updatedTestimonial = testimonialService.saveTestimonial(testimonial);
            return ResponseEntity.ok(updatedTestimonial);
        }
        return ResponseEntity.notFound().build();
    }
    
    // Partially update testimonial
    @PatchMapping("/{id}")
    public ResponseEntity<Testimonial> patchTestimonial(@PathVariable String id, @RequestBody Testimonial testimonialUpdates) {
        Optional<Testimonial> existingTestimonialOpt = testimonialService.getTestimonialById(id);
        if (existingTestimonialOpt.isPresent()) {
            Testimonial existingTestimonial = existingTestimonialOpt.get();
            
            // Update only non-null fields
            if (testimonialUpdates.getName() != null) existingTestimonial.setName(testimonialUpdates.getName());
            if (testimonialUpdates.getRank() != null) existingTestimonial.setRank(testimonialUpdates.getRank());
            if (testimonialUpdates.getImage() != null) existingTestimonial.setImage(testimonialUpdates.getImage());
            if (testimonialUpdates.getQuote() != null) existingTestimonial.setQuote(testimonialUpdates.getQuote());
            if (testimonialUpdates.getFeatured() != null) existingTestimonial.setFeatured(testimonialUpdates.getFeatured());
            
            Testimonial updatedTestimonial = testimonialService.saveTestimonial(existingTestimonial);
            return ResponseEntity.ok(updatedTestimonial);
        }
        return ResponseEntity.notFound().build();
    }
    
    // Delete testimonial
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteTestimonial(@PathVariable String id) {
        Optional<Testimonial> testimonial = testimonialService.getTestimonialById(id);
        if (testimonial.isPresent()) {
            testimonialService.deleteTestimonial(id);
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }
    
    // Get featured testimonials
    @GetMapping("/featured")
    public ResponseEntity<List<Testimonial>> getFeaturedTestimonials() {
        List<Testimonial> testimonials = testimonialService.getFeaturedTestimonials();
        return ResponseEntity.ok(testimonials);
    }
    
    // Update featured status
    @PostMapping("/{id}/featured")
    public ResponseEntity<Testimonial> updateFeaturedStatus(@PathVariable String id, @RequestBody FeaturedRequest request) {
        Testimonial testimonial = testimonialService.updateFeaturedStatus(id, request.getFeatured());
        if (testimonial != null) {
            return ResponseEntity.ok(testimonial);
        }
        return ResponseEntity.notFound().build();
    }
    
    // Get testimonial statistics
    @GetMapping("/stats")
    public ResponseEntity<TestimonialStats> getTestimonialStats() {
        TestimonialStats stats = new TestimonialStats();
        stats.setTotalTestimonials(testimonialService.getTotalTestimonials());
        stats.setFeaturedTestimonialCount(testimonialService.getFeaturedTestimonialCount());
        
        return ResponseEntity.ok(stats);
    }
    
    // Inner classes for request/response DTOs
    public static class FeaturedRequest {
        private Boolean featured;
        
        public Boolean getFeatured() { return featured; }
        public void setFeatured(Boolean featured) { this.featured = featured; }
    }
    
    public static class TestimonialStats {
        private long totalTestimonials;
        private long featuredTestimonialCount;
        
        public long getTotalTestimonials() { return totalTestimonials; }
        public void setTotalTestimonials(long totalTestimonials) { this.totalTestimonials = totalTestimonials; }
        public long getFeaturedTestimonialCount() { return featuredTestimonialCount; }
        public void setFeaturedTestimonialCount(long featuredTestimonialCount) { this.featuredTestimonialCount = featuredTestimonialCount; }
    }
}
