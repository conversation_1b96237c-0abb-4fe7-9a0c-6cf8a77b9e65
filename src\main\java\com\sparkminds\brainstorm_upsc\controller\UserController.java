package com.sparkminds.brainstorm_upsc.controller;

import com.sparkminds.brainstorm_upsc.entity.User;
import com.sparkminds.brainstorm_upsc.service.UserService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/users")
@CrossOrigin(origins = "*")
@Tag(name = "Users", description = "User management and authentication operations")
public class UserController {

    @Autowired
    private UserService userService;

    // Get all users
    @GetMapping
    public ResponseEntity<List<User>> getAllUsers(
            @RequestParam(required = false) String role,
            @RequestParam(required = false) String subscription,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String email) {

        List<User> users;

        if (email != null) {
            Optional<User> user = userService.getUserByEmail(email);
            users = user.map(List::of).orElse(List.of());
        } else if (role != null) {
            users = userService.getUsersByRole(role);
        } else if (subscription != null) {
            users = userService.getUsersBySubscription(subscription);
        } else if (status != null) {
            users = userService.getUsersByStatus(status);
        } else {
            users = userService.getAllUsers();
        }

        return ResponseEntity.ok(users);
    }

    // Get user by ID
    @GetMapping("/{id}")
    public ResponseEntity<User> getUserById(@PathVariable String id) {
        Optional<User> user = userService.getUserById(id);
        return user.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    // Create new user
    @PostMapping
    public ResponseEntity<User> createUser(@RequestBody User user) {
        try {
            User savedUser = userService.saveUser(user);
            return ResponseEntity.status(HttpStatus.CREATED).body(savedUser);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    // Update user
    @PutMapping("/{id}")
    public ResponseEntity<User> updateUser(@PathVariable String id, @RequestBody User user) {
        Optional<User> existingUser = userService.getUserById(id);
        if (existingUser.isPresent()) {
            user.setId(id);
            User updatedUser = userService.saveUser(user);
            return ResponseEntity.ok(updatedUser);
        }
        return ResponseEntity.notFound().build();
    }

    // Partially update user
    @PatchMapping("/{id}")
    public ResponseEntity<User> patchUser(@PathVariable String id, @RequestBody User userUpdates) {
        Optional<User> existingUserOpt = userService.getUserById(id);
        if (existingUserOpt.isPresent()) {
            User existingUser = existingUserOpt.get();

            // Update only non-null fields
            if (userUpdates.getName() != null)
                existingUser.setName(userUpdates.getName());
            if (userUpdates.getEmail() != null)
                existingUser.setEmail(userUpdates.getEmail());
            if (userUpdates.getPhone() != null)
                existingUser.setPhone(userUpdates.getPhone());
            if (userUpdates.getRole() != null)
                existingUser.setRole(userUpdates.getRole());
            if (userUpdates.getSubscription() != null)
                existingUser.setSubscription(userUpdates.getSubscription());
            if (userUpdates.getStatus() != null)
                existingUser.setStatus(userUpdates.getStatus());
            if (userUpdates.getExamsCompleted() != null)
                existingUser.setExamsCompleted(userUpdates.getExamsCompleted());
            if (userUpdates.getAvgScore() != null)
                existingUser.setAvgScore(userUpdates.getAvgScore());
            if (userUpdates.getAvatar() != null)
                existingUser.setAvatar(userUpdates.getAvatar());

            User updatedUser = userService.saveUser(existingUser);
            return ResponseEntity.ok(updatedUser);
        }
        return ResponseEntity.notFound().build();
    }

    // Delete user
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteUser(@PathVariable String id) {
        Optional<User> user = userService.getUserById(id);
        if (user.isPresent()) {
            userService.deleteUser(id);
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }

    // Authenticate user (login)
    @PostMapping("/login")
    public ResponseEntity<User> login(@RequestBody LoginRequest loginRequest) {
        Optional<User> user = userService.authenticateUser(loginRequest.getEmail(), loginRequest.getPassword());
        if (user.isPresent()) {
            userService.updateLastActive(user.get().getId());
            return ResponseEntity.ok(user.get());
        }
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
    }

    // Register new user
    @PostMapping("/register")
    public ResponseEntity<User> register(@RequestBody RegisterRequest registerRequest) {
        try {
            User user = userService.registerUser(
                    registerRequest.getName(),
                    registerRequest.getEmail(),
                    registerRequest.getPassword(),
                    registerRequest.getPhone());
            return ResponseEntity.status(HttpStatus.CREATED).body(user);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    // Get user statistics
    @GetMapping("/stats")
    public ResponseEntity<UserStats> getUserStats() {
        UserStats stats = new UserStats();
        stats.setTotalUsers(userService.getTotalUsers());
        stats.setStudentCount(userService.getStudentCount());
        stats.setAdminCount(userService.getAdminCount());
        stats.setActiveUserCount(userService.getActiveUserCount());
        stats.setPremiumUserCount(userService.getPremiumUserCount());
        stats.setFreeUserCount(userService.getFreeUserCount());

        return ResponseEntity.ok(stats);
    }

    // Search users
    @GetMapping("/search")
    public ResponseEntity<List<User>> searchUsers(@RequestParam String q) {
        List<User> users = userService.searchUsersByName(q);
        return ResponseEntity.ok(users);
    }

    // Get top performers
    @GetMapping("/top-performers")
    public ResponseEntity<List<User>> getTopPerformers() {
        List<User> topPerformers = userService.getTopPerformers();
        return ResponseEntity.ok(topPerformers);
    }

    // Inner classes for request/response DTOs
    public static class LoginRequest {
        private String email;
        private String password;

        // Getters and setters
        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }
    }

    public static class RegisterRequest {
        private String name;
        private String email;
        private String password;
        private String phone;

        // Getters and setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }
    }

    public static class UserStats {
        private long totalUsers;
        private long studentCount;
        private long adminCount;
        private long activeUserCount;
        private long premiumUserCount;
        private long freeUserCount;

        // Getters and setters
        public long getTotalUsers() {
            return totalUsers;
        }

        public void setTotalUsers(long totalUsers) {
            this.totalUsers = totalUsers;
        }

        public long getStudentCount() {
            return studentCount;
        }

        public void setStudentCount(long studentCount) {
            this.studentCount = studentCount;
        }

        public long getAdminCount() {
            return adminCount;
        }

        public void setAdminCount(long adminCount) {
            this.adminCount = adminCount;
        }

        public long getActiveUserCount() {
            return activeUserCount;
        }

        public void setActiveUserCount(long activeUserCount) {
            this.activeUserCount = activeUserCount;
        }

        public long getPremiumUserCount() {
            return premiumUserCount;
        }

        public void setPremiumUserCount(long premiumUserCount) {
            this.premiumUserCount = premiumUserCount;
        }

        public long getFreeUserCount() {
            return freeUserCount;
        }

        public void setFreeUserCount(long freeUserCount) {
            this.freeUserCount = freeUserCount;
        }
    }
}
