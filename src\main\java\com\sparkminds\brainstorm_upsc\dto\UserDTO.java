package com.sparkminds.brainstorm_upsc.dto;

import java.time.LocalDate;

public class UserDTO {
    private String id;
    private String name;
    private String email;
    private String phone;
    private String role;
    private String subscription;
    private LocalDate joinedAt;
    private LocalDate lastActive;
    private String status;
    private Integer examsCompleted;
    private Double avgScore;
    private String avatar;
    
    // Constructors
    public UserDTO() {}
    
    public UserDTO(String id, String name, String email, String role, String subscription, String status) {
        this.id = id;
        this.name = name;
        this.email = email;
        this.role = role;
        this.subscription = subscription;
        this.status = status;
    }
    
    // Getters and setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getRole() {
        return role;
    }
    
    public void setRole(String role) {
        this.role = role;
    }
    
    public String getSubscription() {
        return subscription;
    }
    
    public void setSubscription(String subscription) {
        this.subscription = subscription;
    }
    
    public LocalDate getJoinedAt() {
        return joinedAt;
    }
    
    public void setJoinedAt(LocalDate joinedAt) {
        this.joinedAt = joinedAt;
    }
    
    public LocalDate getLastActive() {
        return lastActive;
    }
    
    public void setLastActive(LocalDate lastActive) {
        this.lastActive = lastActive;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public Integer getExamsCompleted() {
        return examsCompleted;
    }
    
    public void setExamsCompleted(Integer examsCompleted) {
        this.examsCompleted = examsCompleted;
    }
    
    public Double getAvgScore() {
        return avgScore;
    }
    
    public void setAvgScore(Double avgScore) {
        this.avgScore = avgScore;
    }
    
    public String getAvatar() {
        return avatar;
    }
    
    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }
}
