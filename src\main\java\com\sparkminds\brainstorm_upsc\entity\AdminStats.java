package com.sparkminds.brainstorm_upsc.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Document(collection = "adminStats")
public class AdminStats {
    
    @Id
    private String id;
    
    @Field("label")
    private String label;
    
    @Field("value")
    private String value;
    
    @Field("change")
    private String change;
    
    @Field("icon")
    private String icon;
    
    @Field("color")
    private String color;
    
    // Constructors
    public AdminStats() {}
    
    public AdminStats(String label, String value, String change, String icon, String color) {
        this.label = label;
        this.value = value;
        this.change = change;
        this.icon = icon;
        this.color = color;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getLabel() {
        return label;
    }
    
    public void setLabel(String label) {
        this.label = label;
    }
    
    public String getValue() {
        return value;
    }
    
    public void setValue(String value) {
        this.value = value;
    }
    
    public String getChange() {
        return change;
    }
    
    public void setChange(String change) {
        this.change = change;
    }
    
    public String getIcon() {
        return icon;
    }
    
    public void setIcon(String icon) {
        this.icon = icon;
    }
    
    public String getColor() {
        return color;
    }
    
    public void setColor(String color) {
        this.color = color;
    }
    
    @Override
    public String toString() {
        return "AdminStats{" +
                "id='" + id + '\'' +
                ", label='" + label + '\'' +
                ", value='" + value + '\'' +
                ", change='" + change + '\'' +
                ", icon='" + icon + '\'' +
                ", color='" + color + '\'' +
                '}';
    }
}
