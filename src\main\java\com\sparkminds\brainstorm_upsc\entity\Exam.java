package com.sparkminds.brainstorm_upsc.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Document(collection = "exams")
public class Exam {
    
    @Id
    private String id;
    
    @Field("title")
    private String title;
    
    @Field("description")
    private String description;
    
    @Field("type")
    private String type; // "Mock Test", "Subject Test", "Current Affairs"
    
    @Field("duration")
    private Integer duration; // in minutes
    
    @Field("questions")
    private Integer questions;
    
    @Field("participants")
    private Integer participants;
    
    @Field("difficulty")
    private String difficulty; // "Easy", "Medium", "Hard"
    
    @Field("status")
    private String status; // "published", "draft"
    
    @Field("startDate")
    private LocalDate startDate;
    
    @Field("startTime")
    private String startTime;
    
    @Field("createdAt")
    private LocalDate createdAt;
    
    @Field("scheduledAt")
    private String scheduledAt;
    
    @Field("creator")
    private String creator;
    
    // Constructors
    public Exam() {}
    
    public Exam(String title, String description, String type, Integer duration, 
                Integer questions, String difficulty) {
        this.title = title;
        this.description = description;
        this.type = type;
        this.duration = duration;
        this.questions = questions;
        this.difficulty = difficulty;
        this.participants = 0;
        this.status = "draft";
        this.createdAt = LocalDate.now();
        this.creator = "Admin";
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public Integer getDuration() {
        return duration;
    }
    
    public void setDuration(Integer duration) {
        this.duration = duration;
    }
    
    public Integer getQuestions() {
        return questions;
    }
    
    public void setQuestions(Integer questions) {
        this.questions = questions;
    }
    
    public Integer getParticipants() {
        return participants;
    }
    
    public void setParticipants(Integer participants) {
        this.participants = participants;
    }
    
    public String getDifficulty() {
        return difficulty;
    }
    
    public void setDifficulty(String difficulty) {
        this.difficulty = difficulty;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public LocalDate getStartDate() {
        return startDate;
    }
    
    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }
    
    public String getStartTime() {
        return startTime;
    }
    
    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }
    
    public LocalDate getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDate createdAt) {
        this.createdAt = createdAt;
    }
    
    public String getScheduledAt() {
        return scheduledAt;
    }
    
    public void setScheduledAt(String scheduledAt) {
        this.scheduledAt = scheduledAt;
    }
    
    public String getCreator() {
        return creator;
    }
    
    public void setCreator(String creator) {
        this.creator = creator;
    }
    
    @Override
    public String toString() {
        return "Exam{" +
                "id='" + id + '\'' +
                ", title='" + title + '\'' +
                ", type='" + type + '\'' +
                ", duration=" + duration +
                ", questions=" + questions +
                ", difficulty='" + difficulty + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
