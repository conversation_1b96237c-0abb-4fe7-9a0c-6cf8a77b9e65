package com.sparkminds.brainstorm_upsc.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDateTime;

@Document(collection = "notifications")
public class Notification {
    
    @Id
    private String id;
    
    @Field("title")
    private String title;
    
    @Field("message")
    private String message;
    
    @Field("type")
    private String type; // "info", "success", "warning", "error"
    
    @Field("timestamp")
    private LocalDateTime timestamp;
    
    @Field("read")
    private Boolean read;
    
    @Field("audience")
    private String audience; // "all", "premium", "students", "admins"
    
    @Field("status")
    private String status; // "sent", "scheduled", "draft"
    
    @Field("sentAt")
    private String sentAt;
    
    @Field("recipients")
    private Integer recipients;
    
    @Field("openRate")
    private Integer openRate;
    
    // Constructors
    public Notification() {}
    
    public Notification(String title, String message, String type, String audience) {
        this.title = title;
        this.message = message;
        this.type = type;
        this.audience = audience;
        this.timestamp = LocalDateTime.now();
        this.read = false;
        this.status = "draft";
        this.recipients = 0;
        this.openRate = 0;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
    
    public Boolean getRead() {
        return read;
    }
    
    public void setRead(Boolean read) {
        this.read = read;
    }
    
    public String getAudience() {
        return audience;
    }
    
    public void setAudience(String audience) {
        this.audience = audience;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getSentAt() {
        return sentAt;
    }
    
    public void setSentAt(String sentAt) {
        this.sentAt = sentAt;
    }
    
    public Integer getRecipients() {
        return recipients;
    }
    
    public void setRecipients(Integer recipients) {
        this.recipients = recipients;
    }
    
    public Integer getOpenRate() {
        return openRate;
    }
    
    public void setOpenRate(Integer openRate) {
        this.openRate = openRate;
    }
    
    @Override
    public String toString() {
        return "Notification{" +
                "id='" + id + '\'' +
                ", title='" + title + '\'' +
                ", type='" + type + '\'' +
                ", audience='" + audience + '\'' +
                ", status='" + status + '\'' +
                ", timestamp=" + timestamp +
                '}';
    }
}
