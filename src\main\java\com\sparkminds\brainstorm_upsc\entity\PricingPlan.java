package com.sparkminds.brainstorm_upsc.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.List;

@Document(collection = "pricingPlans")
public class PricingPlan {
    
    @Id
    private String id;
    
    @Field("name")
    private String name;
    
    @Field("price")
    private String price;
    
    @Field("period")
    private String period;
    
    @Field("originalPrice")
    private String originalPrice;
    
    @Field("features")
    private List<String> features;
    
    @Field("popular")
    private Boolean popular;
    
    @Field("active")
    private Boolean active;
    
    // Constructors
    public PricingPlan() {}
    
    public PricingPlan(String name, String price, String period, List<String> features) {
        this.name = name;
        this.price = price;
        this.period = period;
        this.features = features;
        this.popular = false;
        this.active = true;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getPrice() {
        return price;
    }
    
    public void setPrice(String price) {
        this.price = price;
    }
    
    public String getPeriod() {
        return period;
    }
    
    public void setPeriod(String period) {
        this.period = period;
    }
    
    public String getOriginalPrice() {
        return originalPrice;
    }
    
    public void setOriginalPrice(String originalPrice) {
        this.originalPrice = originalPrice;
    }
    
    public List<String> getFeatures() {
        return features;
    }
    
    public void setFeatures(List<String> features) {
        this.features = features;
    }
    
    public Boolean getPopular() {
        return popular;
    }
    
    public void setPopular(Boolean popular) {
        this.popular = popular;
    }
    
    public Boolean getActive() {
        return active;
    }
    
    public void setActive(Boolean active) {
        this.active = active;
    }
    
    @Override
    public String toString() {
        return "PricingPlan{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", price='" + price + '\'' +
                ", period='" + period + '\'' +
                ", popular=" + popular +
                ", active=" + active +
                '}';
    }
}
