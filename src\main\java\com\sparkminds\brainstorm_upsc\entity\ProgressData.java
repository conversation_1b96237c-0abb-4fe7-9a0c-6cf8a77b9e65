package com.sparkminds.brainstorm_upsc.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Document(collection = "progressData")
public class ProgressData {
    
    @Id
    private String id;
    
    @Field("month")
    private String month;
    
    @Field("score")
    private Integer score;
    
    // Constructors
    public ProgressData() {}
    
    public ProgressData(String month, Integer score) {
        this.month = month;
        this.score = score;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getMonth() {
        return month;
    }
    
    public void setMonth(String month) {
        this.month = month;
    }
    
    public Integer getScore() {
        return score;
    }
    
    public void setScore(Integer score) {
        this.score = score;
    }
    
    @Override
    public String toString() {
        return "ProgressData{" +
                "id='" + id + '\'' +
                ", month='" + month + '\'' +
                ", score=" + score +
                '}';
    }
}
