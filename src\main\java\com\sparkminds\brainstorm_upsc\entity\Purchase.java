package com.sparkminds.brainstorm_upsc.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDateTime;

@Document(collection = "purchases")
public class Purchase {
    
    @Id
    private String id;
    
    @Field("userId")
    private String userId;
    
    @Field("materialId")
    private String materialId;
    
    @Field("paymentId")
    private String paymentId;
    
    @Field("orderId")
    private String orderId;
    
    @Field("amount")
    private Double amount;
    
    @Field("currency")
    private String currency;
    
    @Field("paymentMethod")
    private String paymentMethod; // "razorpay", "cashfree", "upi"
    
    @Field("status")
    private String status; // "completed", "pending", "failed"
    
    @Field("purchaseDate")
    private LocalDateTime purchaseDate;
    
    @Field("accessGranted")
    private Boolean accessGranted;
    
    @Field("downloadCount")
    private Integer downloadCount;
    
    @Field("lastAccessedAt")
    private LocalDateTime lastAccessedAt;
    
    // Constructors
    public Purchase() {}
    
    public Purchase(String userId, String materialId, Double amount, String paymentMethod) {
        this.userId = userId;
        this.materialId = materialId;
        this.amount = amount;
        this.paymentMethod = paymentMethod;
        this.currency = "INR";
        this.status = "pending";
        this.purchaseDate = LocalDateTime.now();
        this.accessGranted = false;
        this.downloadCount = 0;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getUserId() {
        return userId;
    }
    
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    public String getMaterialId() {
        return materialId;
    }
    
    public void setMaterialId(String materialId) {
        this.materialId = materialId;
    }
    
    public String getPaymentId() {
        return paymentId;
    }
    
    public void setPaymentId(String paymentId) {
        this.paymentId = paymentId;
    }
    
    public String getOrderId() {
        return orderId;
    }
    
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }
    
    public Double getAmount() {
        return amount;
    }
    
    public void setAmount(Double amount) {
        this.amount = amount;
    }
    
    public String getCurrency() {
        return currency;
    }
    
    public void setCurrency(String currency) {
        this.currency = currency;
    }
    
    public String getPaymentMethod() {
        return paymentMethod;
    }
    
    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public LocalDateTime getPurchaseDate() {
        return purchaseDate;
    }
    
    public void setPurchaseDate(LocalDateTime purchaseDate) {
        this.purchaseDate = purchaseDate;
    }
    
    public Boolean getAccessGranted() {
        return accessGranted;
    }
    
    public void setAccessGranted(Boolean accessGranted) {
        this.accessGranted = accessGranted;
    }
    
    public Integer getDownloadCount() {
        return downloadCount;
    }
    
    public void setDownloadCount(Integer downloadCount) {
        this.downloadCount = downloadCount;
    }
    
    public LocalDateTime getLastAccessedAt() {
        return lastAccessedAt;
    }
    
    public void setLastAccessedAt(LocalDateTime lastAccessedAt) {
        this.lastAccessedAt = lastAccessedAt;
    }
    
    @Override
    public String toString() {
        return "Purchase{" +
                "id='" + id + '\'' +
                ", userId='" + userId + '\'' +
                ", materialId='" + materialId + '\'' +
                ", amount=" + amount +
                ", status='" + status + '\'' +
                ", purchaseDate=" + purchaseDate +
                '}';
    }
}
