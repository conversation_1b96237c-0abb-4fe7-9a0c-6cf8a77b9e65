package com.sparkminds.brainstorm_upsc.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.List;

@Document(collection = "questions")
public class Question {
    
    @Id
    private String id;
    
    @Field("examId")
    private String examId;
    
    @Field("question")
    private String question;
    
    @Field("options")
    private List<String> options;
    
    @Field("correct")
    private Integer correct; // 0-based index of correct answer
    
    @Field("explanation")
    private String explanation;
    
    // Constructors
    public Question() {}
    
    public Question(String examId, String question, List<String> options, 
                   Integer correct, String explanation) {
        this.examId = examId;
        this.question = question;
        this.options = options;
        this.correct = correct;
        this.explanation = explanation;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getExamId() {
        return examId;
    }
    
    public void setExamId(String examId) {
        this.examId = examId;
    }
    
    public String getQuestion() {
        return question;
    }
    
    public void setQuestion(String question) {
        this.question = question;
    }
    
    public List<String> getOptions() {
        return options;
    }
    
    public void setOptions(List<String> options) {
        this.options = options;
    }
    
    public Integer getCorrect() {
        return correct;
    }
    
    public void setCorrect(Integer correct) {
        this.correct = correct;
    }
    
    public String getExplanation() {
        return explanation;
    }
    
    public void setExplanation(String explanation) {
        this.explanation = explanation;
    }
    
    @Override
    public String toString() {
        return "Question{" +
                "id='" + id + '\'' +
                ", examId='" + examId + '\'' +
                ", question='" + question + '\'' +
                ", correct=" + correct +
                '}';
    }
}
