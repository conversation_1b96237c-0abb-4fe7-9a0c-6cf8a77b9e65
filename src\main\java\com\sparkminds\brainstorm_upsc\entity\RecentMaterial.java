package com.sparkminds.brainstorm_upsc.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Document(collection = "recentMaterials")
public class RecentMaterial {
    
    @Id
    private String id;
    
    @Field("title")
    private String title;
    
    @Field("type")
    private String type;
    
    @Field("price")
    private String price;
    
    // Constructors
    public RecentMaterial() {}
    
    public RecentMaterial(String title, String type, String price) {
        this.title = title;
        this.type = type;
        this.price = price;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getPrice() {
        return price;
    }
    
    public void setPrice(String price) {
        this.price = price;
    }
    
    @Override
    public String toString() {
        return "RecentMaterial{" +
                "id='" + id + '\'' +
                ", title='" + title + '\'' +
                ", type='" + type + '\'' +
                ", price='" + price + '\'' +
                '}';
    }
}
