package com.sparkminds.brainstorm_upsc.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDate;

@Document(collection = "studyMaterials")
public class StudyMaterial {
    
    @Id
    private String id;
    
    @Field("title")
    private String title;
    
    @Field("description")
    private String description;
    
    @Field("type")
    private String type; // "PDF", "Video", "Audio"
    
    @Field("subject")
    private String subject;
    
    @Field("price")
    private Double price;
    
    @Field("originalPrice")
    private Double originalPrice;
    
    @Field("rating")
    private Double rating;
    
    @Field("reviews")
    private Integer reviews;
    
    @Field("pages")
    private Integer pages;
    
    @Field("duration")
    private String duration; // for video/audio materials
    
    @Field("author")
    private String author;
    
    @Field("isPremium")
    private Boolean isPremium;
    
    @Field("thumbnailUrl")
    private String thumbnailUrl;
    
    @Field("status")
    private String status; // "published", "draft"
    
    @Field("downloads")
    private Integer downloads;
    
    @Field("createdAt")
    private LocalDate createdAt;
    
    @Field("fileSize")
    private String fileSize;
    
    // Constructors
    public StudyMaterial() {}
    
    public StudyMaterial(String title, String description, String type, String subject, 
                        Double price, String author) {
        this.title = title;
        this.description = description;
        this.type = type;
        this.subject = subject;
        this.price = price;
        this.author = author;
        this.rating = 0.0;
        this.reviews = 0;
        this.isPremium = false;
        this.status = "draft";
        this.downloads = 0;
        this.createdAt = LocalDate.now();
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getSubject() {
        return subject;
    }
    
    public void setSubject(String subject) {
        this.subject = subject;
    }
    
    public Double getPrice() {
        return price;
    }
    
    public void setPrice(Double price) {
        this.price = price;
    }
    
    public Double getOriginalPrice() {
        return originalPrice;
    }
    
    public void setOriginalPrice(Double originalPrice) {
        this.originalPrice = originalPrice;
    }
    
    public Double getRating() {
        return rating;
    }
    
    public void setRating(Double rating) {
        this.rating = rating;
    }
    
    public Integer getReviews() {
        return reviews;
    }
    
    public void setReviews(Integer reviews) {
        this.reviews = reviews;
    }
    
    public Integer getPages() {
        return pages;
    }
    
    public void setPages(Integer pages) {
        this.pages = pages;
    }
    
    public String getDuration() {
        return duration;
    }
    
    public void setDuration(String duration) {
        this.duration = duration;
    }
    
    public String getAuthor() {
        return author;
    }
    
    public void setAuthor(String author) {
        this.author = author;
    }
    
    public Boolean getIsPremium() {
        return isPremium;
    }
    
    public void setIsPremium(Boolean isPremium) {
        this.isPremium = isPremium;
    }
    
    public String getThumbnailUrl() {
        return thumbnailUrl;
    }
    
    public void setThumbnailUrl(String thumbnailUrl) {
        this.thumbnailUrl = thumbnailUrl;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public Integer getDownloads() {
        return downloads;
    }
    
    public void setDownloads(Integer downloads) {
        this.downloads = downloads;
    }
    
    public LocalDate getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDate createdAt) {
        this.createdAt = createdAt;
    }
    
    public String getFileSize() {
        return fileSize;
    }
    
    public void setFileSize(String fileSize) {
        this.fileSize = fileSize;
    }
    
    @Override
    public String toString() {
        return "StudyMaterial{" +
                "id='" + id + '\'' +
                ", title='" + title + '\'' +
                ", type='" + type + '\'' +
                ", subject='" + subject + '\'' +
                ", price=" + price +
                ", status='" + status + '\'' +
                '}';
    }
}
