package com.sparkminds.brainstorm_upsc.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Document(collection = "testimonials")
public class Testimonial {
    
    @Id
    private String id;
    
    @Field("name")
    private String name;
    
    @Field("rank")
    private String rank;
    
    @Field("image")
    private String image;
    
    @Field("quote")
    private String quote;
    
    @Field("featured")
    private Boolean featured;
    
    // Constructors
    public Testimonial() {}
    
    public Testimonial(String name, String rank, String quote) {
        this.name = name;
        this.rank = rank;
        this.quote = quote;
        this.featured = false;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getRank() {
        return rank;
    }
    
    public void setRank(String rank) {
        this.rank = rank;
    }
    
    public String getImage() {
        return image;
    }
    
    public void setImage(String image) {
        this.image = image;
    }
    
    public String getQuote() {
        return quote;
    }
    
    public void setQuote(String quote) {
        this.quote = quote;
    }
    
    public Boolean getFeatured() {
        return featured;
    }
    
    public void setFeatured(Boolean featured) {
        this.featured = featured;
    }
    
    @Override
    public String toString() {
        return "Testimonial{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", rank='" + rank + '\'' +
                ", featured=" + featured +
                '}';
    }
}
