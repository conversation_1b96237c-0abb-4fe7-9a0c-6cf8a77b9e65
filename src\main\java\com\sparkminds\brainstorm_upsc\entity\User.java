package com.sparkminds.brainstorm_upsc.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.index.Indexed;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Document(collection = "users")
public class User {
    
    @Id
    private String id;
    
    @Field("name")
    private String name;
    
    @Field("email")
    @Indexed(unique = true)
    private String email;
    
    @Field("password")
    private String password;
    
    @Field("phone")
    private String phone;
    
    @Field("role")
    private String role; // "student" or "admin"
    
    @Field("subscription")
    private String subscription; // "free", "premium", "elite"
    
    @Field("joinedAt")
    private LocalDate joinedAt;
    
    @Field("lastActive")
    private LocalDate lastActive;
    
    @Field("status")
    private String status; // "active", "inactive"
    
    @Field("examsCompleted")
    private Integer examsCompleted;
    
    @Field("avgScore")
    private Double avgScore;
    
    @Field("avatar")
    private String avatar;
    
    // Constructors
    public User() {}
    
    public User(String name, String email, String password, String phone, String role) {
        this.name = name;
        this.email = email;
        this.password = password;
        this.phone = phone;
        this.role = role;
        this.subscription = "free";
        this.joinedAt = LocalDate.now();
        this.lastActive = LocalDate.now();
        this.status = "active";
        this.examsCompleted = 0;
        this.avgScore = 0.0;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getRole() {
        return role;
    }
    
    public void setRole(String role) {
        this.role = role;
    }
    
    public String getSubscription() {
        return subscription;
    }
    
    public void setSubscription(String subscription) {
        this.subscription = subscription;
    }
    
    public LocalDate getJoinedAt() {
        return joinedAt;
    }
    
    public void setJoinedAt(LocalDate joinedAt) {
        this.joinedAt = joinedAt;
    }
    
    public LocalDate getLastActive() {
        return lastActive;
    }
    
    public void setLastActive(LocalDate lastActive) {
        this.lastActive = lastActive;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public Integer getExamsCompleted() {
        return examsCompleted;
    }
    
    public void setExamsCompleted(Integer examsCompleted) {
        this.examsCompleted = examsCompleted;
    }
    
    public Double getAvgScore() {
        return avgScore;
    }
    
    public void setAvgScore(Double avgScore) {
        this.avgScore = avgScore;
    }
    
    public String getAvatar() {
        return avatar;
    }
    
    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }
    
    @Override
    public String toString() {
        return "User{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", email='" + email + '\'' +
                ", role='" + role + '\'' +
                ", subscription='" + subscription + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
