package com.sparkminds.brainstorm_upsc.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Document(collection = "userGrowthData")
public class UserGrowthData {
    
    @Id
    private String id;
    
    @Field("month")
    private String month;
    
    @Field("users")
    private Integer users;
    
    // Constructors
    public UserGrowthData() {}
    
    public UserGrowthData(String month, Integer users) {
        this.month = month;
        this.users = users;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getMonth() {
        return month;
    }
    
    public void setMonth(String month) {
        this.month = month;
    }
    
    public Integer getUsers() {
        return users;
    }
    
    public void setUsers(Integer users) {
        this.users = users;
    }
    
    @Override
    public String toString() {
        return "UserGrowthData{" +
                "id='" + id + '\'' +
                ", month='" + month + '\'' +
                ", users=" + users +
                '}';
    }
}
