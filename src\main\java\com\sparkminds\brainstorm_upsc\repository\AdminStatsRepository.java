package com.sparkminds.brainstorm_upsc.repository;

import com.sparkminds.brainstorm_upsc.entity.AdminStats;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface AdminStatsRepository extends MongoRepository<AdminStats, String> {
    
    // Find stats by label
    Optional<AdminStats> findByLabel(String label);
}
