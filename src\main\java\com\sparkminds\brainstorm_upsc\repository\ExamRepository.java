package com.sparkminds.brainstorm_upsc.repository;

import com.sparkminds.brainstorm_upsc.entity.Exam;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface ExamRepository extends MongoRepository<Exam, String> {
    
    // Find exams by type
    List<Exam> findByType(String type);
    
    // Find exams by status
    List<Exam> findByStatus(String status);
    
    // Find exams by difficulty
    List<Exam> findByDifficulty(String difficulty);
    
    // Find published exams
    List<Exam> findByStatusOrderByCreatedAtDesc(String status);
    
    // Find exams by type and status
    List<Exam> findByTypeAndStatus(String type, String status);
    
    // Find exams by difficulty and status
    List<Exam> findByDifficultyAndStatus(String difficulty, String status);
    
    // Find exams by creator
    List<Exam> findByCreator(String creator);
    
    // Find exams created after specific date
    List<Exam> findByCreatedAtAfter(LocalDate date);
    
    // Find exams scheduled after specific date
    List<Exam> findByStartDateAfter(LocalDate date);
    
    // Find exams scheduled between dates
    List<Exam> findByStartDateBetween(LocalDate startDate, LocalDate endDate);
    
    // Find exams by duration range
    List<Exam> findByDurationBetween(Integer minDuration, Integer maxDuration);
    
    // Find exams with participants greater than specified number
    List<Exam> findByParticipantsGreaterThan(Integer participants);
    
    // Custom query to search exams by title or description
    @Query("{'$and': [{'status': 'published'}, {'$or': [{'title': {$regex: ?0, $options: 'i'}}, {'description': {$regex: ?0, $options: 'i'}}]}]}")
    List<Exam> searchByTitleOrDescription(String searchTerm);
    
    // Find upcoming exams (published and scheduled for future)
    @Query("{'status': 'published', 'startDate': {$gte: ?0}}")
    List<Exam> findUpcomingExams(LocalDate currentDate);
    
    // Find popular exams (high participants)
    @Query("{'status': 'published', 'participants': {$gte: ?0}}")
    List<Exam> findPopularExams(Integer minParticipants);
    
    // Find exams by type ordered by participants
    List<Exam> findByTypeAndStatusOrderByParticipantsDesc(String type, String status);
    
    // Find recent exams
    List<Exam> findTop10ByStatusOrderByCreatedAtDesc(String status);
    
    // Find most popular exams
    List<Exam> findTop10ByStatusOrderByParticipantsDesc(String status);
    
    // Count exams by type
    long countByType(String type);
    
    // Count exams by status
    long countByStatus(String status);
    
    // Count exams by difficulty
    long countByDifficulty(String difficulty);
    
    // Find exams by multiple types
    @Query("{'type': {$in: ?0}, 'status': 'published'}")
    List<Exam> findByTypeIn(List<String> types);
    
    // Find today's exams
    List<Exam> findByStartDateAndStatus(LocalDate date, String status);
    
    // Find exams scheduled for this week
    @Query("{'startDate': {$gte: ?0, $lte: ?1}, 'status': 'published'}")
    List<Exam> findExamsThisWeek(LocalDate startOfWeek, LocalDate endOfWeek);
    
    // Find mock tests
    @Query("{'type': 'Mock Test', 'status': 'published'}")
    List<Exam> findMockTests();
    
    // Find subject tests
    @Query("{'type': 'Subject Test', 'status': 'published'}")
    List<Exam> findSubjectTests();
    
    // Find current affairs tests
    @Query("{'type': 'Current Affairs', 'status': 'published'}")
    List<Exam> findCurrentAffairsTests();
}
