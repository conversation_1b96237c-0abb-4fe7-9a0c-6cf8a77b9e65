package com.sparkminds.brainstorm_upsc.repository;

import com.sparkminds.brainstorm_upsc.entity.Notification;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface NotificationRepository extends MongoRepository<Notification, String> {
    
    // Find notifications by type
    List<Notification> findByType(String type);
    
    // Find notifications by audience
    List<Notification> findByAudience(String audience);
    
    // Find notifications by status
    List<Notification> findByStatus(String status);
    
    // Find notifications by read status
    List<Notification> findByRead(Boolean read);
    
    // Find notifications by audience and status
    List<Notification> findByAudienceAndStatus(String audience, String status);
    
    // Find notifications after specific timestamp
    List<Notification> findByTimestampAfter(LocalDateTime timestamp);
    
    // Find recent notifications ordered by timestamp
    List<Notification> findTop10ByOrderByTimestampDesc();
    
    // Find unread notifications for specific audience
    List<Notification> findByAudienceAndReadOrderByTimestampDesc(String audience, Boolean read);
    
    // Count unread notifications
    long countByRead(Boolean read);
    
    // Count notifications by status
    long countByStatus(String status);
}
