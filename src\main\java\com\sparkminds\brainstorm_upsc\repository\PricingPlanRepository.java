package com.sparkminds.brainstorm_upsc.repository;

import com.sparkminds.brainstorm_upsc.entity.PricingPlan;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface PricingPlanRepository extends MongoRepository<PricingPlan, String> {
    
    // Find active pricing plans
    List<PricingPlan> findByActive(Boolean active);
    
    // Find popular pricing plan
    Optional<PricingPlan> findByPopular(Boolean popular);
    
    // Find pricing plan by name
    Optional<PricingPlan> findByName(String name);
    
    // Find active plans ordered by popular status
    List<PricingPlan> findByActiveOrderByPopularDesc(Boolean active);
}
