package com.sparkminds.brainstorm_upsc.repository;

import com.sparkminds.brainstorm_upsc.entity.ProgressData;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ProgressDataRepository extends MongoRepository<ProgressData, String> {
    
    // Find progress data by month
    Optional<ProgressData> findByMonth(String month);
    
    // Find all progress data ordered by month
    List<ProgressData> findAllByOrderByMonth();
    
    // Find progress data with score greater than specified value
    List<ProgressData> findByScoreGreaterThan(Integer score);
}
