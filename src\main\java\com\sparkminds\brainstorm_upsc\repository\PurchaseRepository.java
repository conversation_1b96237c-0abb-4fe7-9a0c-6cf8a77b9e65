package com.sparkminds.brainstorm_upsc.repository;

import com.sparkminds.brainstorm_upsc.entity.Purchase;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface PurchaseRepository extends MongoRepository<Purchase, String> {
    
    // Find purchases by user ID
    List<Purchase> findByUserId(String userId);
    
    // Find purchases by material ID
    List<Purchase> findByMaterialId(String materialId);
    
    // Find purchases by user ID and material ID
    List<Purchase> findByUserIdAndMaterialId(String userId, String materialId);
    
    // Find purchases by status
    List<Purchase> findByStatus(String status);
    
    // Find purchases by payment method
    List<Purchase> findByPaymentMethod(String paymentMethod);
    
    // Find completed purchases by user
    List<Purchase> findByUserIdAndStatus(String userId, String status);
    
    // Find purchases by user and status ordered by date
    List<Purchase> findByUserIdAndStatusOrderByPurchaseDateDesc(String userId, String status);
    
    // Find purchase by payment ID
    Optional<Purchase> findByPaymentId(String paymentId);
    
    // Find purchase by order ID
    Optional<Purchase> findByOrderId(String orderId);
    
    // Find purchases after specific date
    List<Purchase> findByPurchaseDateAfter(LocalDateTime date);
    
    // Find purchases between dates
    List<Purchase> findByPurchaseDateBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    // Find purchases with access granted
    List<Purchase> findByAccessGranted(Boolean accessGranted);
    
    // Find purchases by user with access granted
    List<Purchase> findByUserIdAndAccessGranted(String userId, Boolean accessGranted);
    
    // Check if user has purchased a material
    @Query("{'userId': ?0, 'materialId': ?1, 'status': 'completed', 'accessGranted': true}")
    Optional<Purchase> findUserMaterialPurchase(String userId, String materialId);
    
    // Find recent purchases
    List<Purchase> findTop10ByStatusOrderByPurchaseDateDesc(String status);
    
    // Count purchases by status
    long countByStatus(String status);
    
    // Count purchases by user
    long countByUserId(String userId);
    
    // Count purchases by material
    long countByMaterialId(String materialId);
    
    // Find purchases with download count greater than specified
    List<Purchase> findByDownloadCountGreaterThan(Integer downloadCount);
    
    // Calculate total revenue
    @Query(value = "{'status': 'completed'}", fields = "{'amount': 1}")
    List<Purchase> findCompletedPurchasesForRevenue();
    
    // Find purchases by amount range
    List<Purchase> findByAmountBetween(Double minAmount, Double maxAmount);
    
    // Find purchases by multiple users
    @Query("{'userId': {$in: ?0}}")
    List<Purchase> findByUserIdIn(List<String> userIds);
    
    // Find purchases by multiple materials
    @Query("{'materialId': {$in: ?0}}")
    List<Purchase> findByMaterialIdIn(List<String> materialIds);
    
    // Find monthly purchases
    @Query("{'purchaseDate': {$gte: ?0, $lt: ?1}, 'status': 'completed'}")
    List<Purchase> findMonthlyPurchases(LocalDateTime startOfMonth, LocalDateTime endOfMonth);
    
    // Find daily purchases
    @Query("{'purchaseDate': {$gte: ?0, $lt: ?1}, 'status': 'completed'}")
    List<Purchase> findDailyPurchases(LocalDateTime startOfDay, LocalDateTime endOfDay);
}
