package com.sparkminds.brainstorm_upsc.repository;

import com.sparkminds.brainstorm_upsc.entity.Question;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface QuestionRepository extends MongoRepository<Question, String> {
    
    // Find questions by exam ID
    List<Question> findByExamId(String examId);
    
    // Count questions by exam ID
    long countByExamId(String examId);
    
    // Find questions by exam ID with limit (for pagination)
    @Query("{'examId': ?0}")
    List<Question> findByExamIdWithLimit(String examId, int limit);
    
    // Search questions by text content
    @Query("{'question': {$regex: ?0, $options: 'i'}}")
    List<Question> searchByQuestionText(String searchTerm);
    
    // Find questions by correct answer index
    List<Question> findByCorrect(Integer correctIndex);
    
    // Find questions with explanations containing specific text
    @Query("{'explanation': {$regex: ?0, $options: 'i'}}")
    List<Question> findByExplanationContaining(String text);
    
    // Find random questions for an exam (useful for practice)
    @Query("{'examId': ?0}")
    List<Question> findRandomQuestionsByExamId(String examId);
    
    // Delete all questions for a specific exam
    void deleteByExamId(String examId);
    
    // Find questions by multiple exam IDs
    @Query("{'examId': {$in: ?0}}")
    List<Question> findByExamIdIn(List<String> examIds);
}
