package com.sparkminds.brainstorm_upsc.repository;

import com.sparkminds.brainstorm_upsc.entity.RecentMaterial;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RecentMaterialRepository extends MongoRepository<RecentMaterial, String> {
    
    // Find materials by type
    List<RecentMaterial> findByType(String type);
    
    // Find materials by title containing
    List<RecentMaterial> findByTitleContainingIgnoreCase(String title);
}
