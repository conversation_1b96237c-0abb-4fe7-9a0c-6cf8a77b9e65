package com.sparkminds.brainstorm_upsc.repository;

import com.sparkminds.brainstorm_upsc.entity.StudyMaterial;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface StudyMaterialRepository extends MongoRepository<StudyMaterial, String> {
    
    // Find materials by type
    List<StudyMaterial> findByType(String type);
    
    // Find materials by subject
    List<StudyMaterial> findBySubject(String subject);
    
    // Find materials by status
    List<StudyMaterial> findByStatus(String status);
    
    // Find published materials
    List<StudyMaterial> findByStatusOrderByCreatedAtDesc(String status);
    
    // Find premium materials
    List<StudyMaterial> findByIsPremium(Boolean isPremium);
    
    // Find materials by type and status
    List<StudyMaterial> findByTypeAndStatus(String type, String status);
    
    // Find materials by subject and status
    List<StudyMaterial> findBySubjectAndStatus(String subject, String status);
    
    // Find materials by price range
    List<StudyMaterial> findByPriceBetween(Double minPrice, Double maxPrice);
    
    // Find materials with rating greater than specified value
    List<StudyMaterial> findByRatingGreaterThanEqual(Double rating);
    
    // Find materials by author
    List<StudyMaterial> findByAuthor(String author);
    
    // Find materials created after specific date
    List<StudyMaterial> findByCreatedAtAfter(LocalDate date);
    
    // Find materials with pagination
    Page<StudyMaterial> findByStatus(String status, Pageable pageable);
    
    // Find materials by type with pagination
    Page<StudyMaterial> findByTypeAndStatus(String type, String status, Pageable pageable);
    
    // Find materials by subject with pagination
    Page<StudyMaterial> findBySubjectAndStatus(String subject, String status, Pageable pageable);
    
    // Custom query to search materials by title or description
    @Query("{'$and': [{'status': 'published'}, {'$or': [{'title': {$regex: ?0, $options: 'i'}}, {'description': {$regex: ?0, $options: 'i'}}]}]}")
    List<StudyMaterial> searchByTitleOrDescription(String searchTerm);
    
    // Custom query to find popular materials (high rating and reviews)
    @Query("{'status': 'published', 'rating': {$gte: ?0}, 'reviews': {$gte: ?1}}")
    List<StudyMaterial> findPopularMaterials(Double minRating, Integer minReviews);
    
    // Find top rated materials
    List<StudyMaterial> findTop10ByStatusOrderByRatingDesc(String status);
    
    // Find most downloaded materials
    List<StudyMaterial> findTop10ByStatusOrderByDownloadsDesc(String status);
    
    // Find recent materials
    List<StudyMaterial> findTop10ByStatusOrderByCreatedAtDesc(String status);
    
    // Count materials by type
    long countByType(String type);
    
    // Count materials by subject
    long countBySubject(String subject);
    
    // Count materials by status
    long countByStatus(String status);
    
    // Count premium materials
    long countByIsPremium(Boolean isPremium);
    
    // Find materials by multiple subjects
    @Query("{'subject': {$in: ?0}, 'status': 'published'}")
    List<StudyMaterial> findBySubjectIn(List<String> subjects);
    
    // Find free materials
    @Query("{'isPremium': false, 'status': 'published'}")
    List<StudyMaterial> findFreeMaterials();
    
    // Find premium materials by subscription type
    @Query("{'isPremium': true, 'status': 'published'}")
    List<StudyMaterial> findPremiumMaterials();
}
