package com.sparkminds.brainstorm_upsc.repository;

import com.sparkminds.brainstorm_upsc.entity.Testimonial;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TestimonialRepository extends MongoRepository<Testimonial, String> {
    
    // Find featured testimonials
    List<Testimonial> findByFeatured(Boolean featured);
    
    // Find testimonials by name
    List<Testimonial> findByNameContainingIgnoreCase(String name);
    
    // Find testimonials ordered by featured status
    List<Testimonial> findAllByOrderByFeaturedDesc();
}
