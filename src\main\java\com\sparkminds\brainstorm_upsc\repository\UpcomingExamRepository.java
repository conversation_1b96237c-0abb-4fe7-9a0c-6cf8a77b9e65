package com.sparkminds.brainstorm_upsc.repository;

import com.sparkminds.brainstorm_upsc.entity.UpcomingExam;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UpcomingExamRepository extends MongoRepository<UpcomingExam, String> {
    
    // Find exams by title containing
    List<UpcomingExam> findByTitleContainingIgnoreCase(String title);
    
    // Find exams by date
    List<UpcomingExam> findByDate(String date);
    
    // Find all exams ordered by date
    List<UpcomingExam> findAllByOrderByDate();
}
