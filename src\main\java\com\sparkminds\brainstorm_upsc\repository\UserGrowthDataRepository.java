package com.sparkminds.brainstorm_upsc.repository;

import com.sparkminds.brainstorm_upsc.entity.UserGrowthData;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserGrowthDataRepository extends MongoRepository<UserGrowthData, String> {
    
    // Find growth data by month
    Optional<UserGrowthData> findByMonth(String month);
    
    // Find all growth data ordered by month
    List<UserGrowthData> findAllByOrderByMonth();
    
    // Find growth data with users greater than specified number
    List<UserGrowthData> findByUsersGreaterThan(Integer users);
}
