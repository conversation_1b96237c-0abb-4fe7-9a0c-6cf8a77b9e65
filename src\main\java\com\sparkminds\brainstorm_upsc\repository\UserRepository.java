package com.sparkminds.brainstorm_upsc.repository;

import com.sparkminds.brainstorm_upsc.entity.User;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends MongoRepository<User, String> {
    
    // Find user by email
    Optional<User> findByEmail(String email);
    
    // Find user by email and password (for authentication)
    Optional<User> findByEmailAndPassword(String email, String password);
    
    // Find users by role
    List<User> findByRole(String role);
    
    // Find users by subscription type
    List<User> findBySubscription(String subscription);
    
    // Find users by status
    List<User> findByStatus(String status);
    
    // Find active users
    List<User> findByStatusAndRole(String status, String role);
    
    // Find users joined after a specific date
    List<User> findByJoinedAtAfter(LocalDate date);
    
    // Find users by subscription and status
    List<User> findBySubscriptionAndStatus(String subscription, String status);
    
    // Count users by role
    long countByRole(String role);
    
    // Count users by subscription
    long countBySubscription(String subscription);
    
    // Count active users
    long countByStatus(String status);
    
    // Find users with exams completed greater than specified number
    List<User> findByExamsCompletedGreaterThan(Integer examsCompleted);
    
    // Find users with average score greater than specified score
    List<User> findByAvgScoreGreaterThan(Double avgScore);
    
    // Custom query to find users by name containing (case insensitive)
    @Query("{'name': {$regex: ?0, $options: 'i'}}")
    List<User> findByNameContainingIgnoreCase(String name);
    
    // Custom query to find premium users with high scores
    @Query("{'subscription': 'premium', 'avgScore': {$gte: ?0}}")
    List<User> findPremiumUsersWithHighScore(Double minScore);
    
    // Check if email exists
    boolean existsByEmail(String email);
    
    // Find top performers
    List<User> findTop10ByOrderByAvgScoreDesc();
    
    // Find recently active users
    List<User> findByLastActiveAfter(LocalDate date);
}
