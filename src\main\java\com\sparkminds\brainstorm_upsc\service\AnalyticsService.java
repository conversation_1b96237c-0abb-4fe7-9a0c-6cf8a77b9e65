package com.sparkminds.brainstorm_upsc.service;

import com.sparkminds.brainstorm_upsc.entity.*;
import com.sparkminds.brainstorm_upsc.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class AnalyticsService {
    
    @Autowired
    private AdminStatsRepository adminStatsRepository;
    
    @Autowired
    private UserGrowthDataRepository userGrowthDataRepository;
    
    @Autowired
    private ProgressDataRepository progressDataRepository;
    
    @Autowired
    private UpcomingExamRepository upcomingExamRepository;
    
    @Autowired
    private RecentMaterialRepository recentMaterialRepository;
    
    // Admin Stats methods
    public List<AdminStats> getAllAdminStats() {
        return adminStatsRepository.findAll();
    }
    
    public Optional<AdminStats> getAdminStatById(String id) {
        return adminStatsRepository.findById(id);
    }
    
    public Optional<AdminStats> getAdminStatByLabel(String label) {
        return adminStatsRepository.findByLabel(label);
    }
    
    public AdminStats saveAdminStats(AdminStats adminStats) {
        return adminStatsRepository.save(adminStats);
    }
    
    public AdminStats updateAdminStats(String label, String value, String change) {
        Optional<AdminStats> statsOpt = adminStatsRepository.findByLabel(label);
        if (statsOpt.isPresent()) {
            AdminStats stats = statsOpt.get();
            stats.setValue(value);
            stats.setChange(change);
            return adminStatsRepository.save(stats);
        }
        return null;
    }
    
    public void deleteAdminStats(String id) {
        adminStatsRepository.deleteById(id);
    }
    
    // User Growth Data methods
    public List<UserGrowthData> getAllUserGrowthData() {
        return userGrowthDataRepository.findAllByOrderByMonth();
    }
    
    public Optional<UserGrowthData> getUserGrowthDataById(String id) {
        return userGrowthDataRepository.findById(id);
    }
    
    public Optional<UserGrowthData> getUserGrowthDataByMonth(String month) {
        return userGrowthDataRepository.findByMonth(month);
    }
    
    public UserGrowthData saveUserGrowthData(UserGrowthData userGrowthData) {
        return userGrowthDataRepository.save(userGrowthData);
    }
    
    public UserGrowthData updateUserGrowthData(String month, Integer users) {
        Optional<UserGrowthData> dataOpt = userGrowthDataRepository.findByMonth(month);
        if (dataOpt.isPresent()) {
            UserGrowthData data = dataOpt.get();
            data.setUsers(users);
            return userGrowthDataRepository.save(data);
        } else {
            return userGrowthDataRepository.save(new UserGrowthData(month, users));
        }
    }
    
    public void deleteUserGrowthData(String id) {
        userGrowthDataRepository.deleteById(id);
    }
    
    // Progress Data methods
    public List<ProgressData> getAllProgressData() {
        return progressDataRepository.findAllByOrderByMonth();
    }
    
    public Optional<ProgressData> getProgressDataById(String id) {
        return progressDataRepository.findById(id);
    }
    
    public Optional<ProgressData> getProgressDataByMonth(String month) {
        return progressDataRepository.findByMonth(month);
    }
    
    public ProgressData saveProgressData(ProgressData progressData) {
        return progressDataRepository.save(progressData);
    }
    
    public ProgressData updateProgressData(String month, Integer score) {
        Optional<ProgressData> dataOpt = progressDataRepository.findByMonth(month);
        if (dataOpt.isPresent()) {
            ProgressData data = dataOpt.get();
            data.setScore(score);
            return progressDataRepository.save(data);
        } else {
            return progressDataRepository.save(new ProgressData(month, score));
        }
    }
    
    public void deleteProgressData(String id) {
        progressDataRepository.deleteById(id);
    }
    
    // Upcoming Exam methods
    public List<UpcomingExam> getAllUpcomingExams() {
        return upcomingExamRepository.findAllByOrderByDate();
    }
    
    public Optional<UpcomingExam> getUpcomingExamById(String id) {
        return upcomingExamRepository.findById(id);
    }
    
    public List<UpcomingExam> getUpcomingExamsByDate(String date) {
        return upcomingExamRepository.findByDate(date);
    }
    
    public List<UpcomingExam> searchUpcomingExams(String title) {
        return upcomingExamRepository.findByTitleContainingIgnoreCase(title);
    }
    
    public UpcomingExam saveUpcomingExam(UpcomingExam upcomingExam) {
        return upcomingExamRepository.save(upcomingExam);
    }
    
    public void deleteUpcomingExam(String id) {
        upcomingExamRepository.deleteById(id);
    }
    
    // Recent Material methods
    public List<RecentMaterial> getAllRecentMaterials() {
        return recentMaterialRepository.findAll();
    }
    
    public Optional<RecentMaterial> getRecentMaterialById(String id) {
        return recentMaterialRepository.findById(id);
    }
    
    public List<RecentMaterial> getRecentMaterialsByType(String type) {
        return recentMaterialRepository.findByType(type);
    }
    
    public List<RecentMaterial> searchRecentMaterials(String title) {
        return recentMaterialRepository.findByTitleContainingIgnoreCase(title);
    }
    
    public RecentMaterial saveRecentMaterial(RecentMaterial recentMaterial) {
        return recentMaterialRepository.save(recentMaterial);
    }
    
    public void deleteRecentMaterial(String id) {
        recentMaterialRepository.deleteById(id);
    }
    
    // Analytics statistics
    public long getTotalAdminStats() {
        return adminStatsRepository.count();
    }
    
    public long getTotalUserGrowthData() {
        return userGrowthDataRepository.count();
    }
    
    public long getTotalProgressData() {
        return progressDataRepository.count();
    }
    
    public long getTotalUpcomingExams() {
        return upcomingExamRepository.count();
    }
    
    public long getTotalRecentMaterials() {
        return recentMaterialRepository.count();
    }
}
