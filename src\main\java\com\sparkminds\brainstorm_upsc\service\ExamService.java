package com.sparkminds.brainstorm_upsc.service;

import com.sparkminds.brainstorm_upsc.entity.Exam;
import com.sparkminds.brainstorm_upsc.repository.ExamRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Service
public class ExamService {
    
    @Autowired
    private ExamRepository examRepository;
    
    // Create or update exam
    public Exam saveExam(Exam exam) {
        return examRepository.save(exam);
    }
    
    // Get all exams
    public List<Exam> getAllExams() {
        return examRepository.findAll();
    }
    
    // Get exam by ID
    public Optional<Exam> getExamById(String id) {
        return examRepository.findById(id);
    }
    
    // Get published exams
    public List<Exam> getPublishedExams() {
        return examRepository.findByStatusOrderByCreatedAtDesc("published");
    }
    
    // Get exams by type
    public List<Exam> getExamsByType(String type) {
        return examRepository.findByTypeAndStatus(type, "published");
    }
    
    // Get exams by difficulty
    public List<Exam> getExamsByDifficulty(String difficulty) {
        return examRepository.findByDifficultyAndStatus(difficulty, "published");
    }
    
    // Get upcoming exams
    public List<Exam> getUpcomingExams() {
        return examRepository.findUpcomingExams(LocalDate.now());
    }
    
    // Get mock tests
    public List<Exam> getMockTests() {
        return examRepository.findMockTests();
    }
    
    // Get subject tests
    public List<Exam> getSubjectTests() {
        return examRepository.findSubjectTests();
    }
    
    // Get current affairs tests
    public List<Exam> getCurrentAffairsTests() {
        return examRepository.findCurrentAffairsTests();
    }
    
    // Get popular exams
    public List<Exam> getPopularExams(Integer minParticipants) {
        return examRepository.findPopularExams(minParticipants);
    }
    
    // Get recent exams
    public List<Exam> getRecentExams() {
        return examRepository.findTop10ByStatusOrderByCreatedAtDesc("published");
    }
    
    // Get most popular exams
    public List<Exam> getMostPopularExams() {
        return examRepository.findTop10ByStatusOrderByParticipantsDesc("published");
    }
    
    // Search exams
    public List<Exam> searchExams(String searchTerm) {
        return examRepository.searchByTitleOrDescription(searchTerm);
    }
    
    // Get exams by creator
    public List<Exam> getExamsByCreator(String creator) {
        return examRepository.findByCreator(creator);
    }
    
    // Get exams created after date
    public List<Exam> getExamsCreatedAfter(LocalDate date) {
        return examRepository.findByCreatedAtAfter(date);
    }
    
    // Get exams scheduled after date
    public List<Exam> getExamsScheduledAfter(LocalDate date) {
        return examRepository.findByStartDateAfter(date);
    }
    
    // Get exams scheduled between dates
    public List<Exam> getExamsScheduledBetween(LocalDate startDate, LocalDate endDate) {
        return examRepository.findByStartDateBetween(startDate, endDate);
    }
    
    // Get today's exams
    public List<Exam> getTodaysExams() {
        return examRepository.findByStartDateAndStatus(LocalDate.now(), "published");
    }
    
    // Get this week's exams
    public List<Exam> getThisWeeksExams() {
        LocalDate startOfWeek = LocalDate.now().minusDays(LocalDate.now().getDayOfWeek().getValue() - 1);
        LocalDate endOfWeek = startOfWeek.plusDays(6);
        return examRepository.findExamsThisWeek(startOfWeek, endOfWeek);
    }
    
    // Update exam status
    public Exam updateExamStatus(String examId, String status) {
        Optional<Exam> examOpt = examRepository.findById(examId);
        if (examOpt.isPresent()) {
            Exam exam = examOpt.get();
            exam.setStatus(status);
            return examRepository.save(exam);
        }
        return null;
    }
    
    // Update participant count
    public Exam incrementParticipantCount(String examId) {
        Optional<Exam> examOpt = examRepository.findById(examId);
        if (examOpt.isPresent()) {
            Exam exam = examOpt.get();
            exam.setParticipants(exam.getParticipants() + 1);
            return examRepository.save(exam);
        }
        return null;
    }
    
    // Update exam schedule
    public Exam updateExamSchedule(String examId, LocalDate startDate, String startTime) {
        Optional<Exam> examOpt = examRepository.findById(examId);
        if (examOpt.isPresent()) {
            Exam exam = examOpt.get();
            exam.setStartDate(startDate);
            exam.setStartTime(startTime);
            exam.setScheduledAt(startDate + " " + startTime);
            return examRepository.save(exam);
        }
        return null;
    }
    
    // Delete exam
    public void deleteExam(String id) {
        examRepository.deleteById(id);
    }
    
    // Get exam statistics
    public long getTotalExams() {
        return examRepository.count();
    }
    
    public long getPublishedExamCount() {
        return examRepository.countByStatus("published");
    }
    
    public long getDraftExamCount() {
        return examRepository.countByStatus("draft");
    }
    
    public long getExamCountByType(String type) {
        return examRepository.countByType(type);
    }
    
    public long getExamCountByDifficulty(String difficulty) {
        return examRepository.countByDifficulty(difficulty);
    }
    
    public long getMockTestCount() {
        return examRepository.countByType("Mock Test");
    }
    
    public long getSubjectTestCount() {
        return examRepository.countByType("Subject Test");
    }
    
    public long getCurrentAffairsTestCount() {
        return examRepository.countByType("Current Affairs");
    }
}
