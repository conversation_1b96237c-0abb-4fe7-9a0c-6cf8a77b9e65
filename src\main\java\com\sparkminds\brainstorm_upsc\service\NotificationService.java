package com.sparkminds.brainstorm_upsc.service;

import com.sparkminds.brainstorm_upsc.entity.Notification;
import com.sparkminds.brainstorm_upsc.repository.NotificationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class NotificationService {
    
    @Autowired
    private NotificationRepository notificationRepository;
    
    // Create or update notification
    public Notification saveNotification(Notification notification) {
        return notificationRepository.save(notification);
    }
    
    // Get all notifications
    public List<Notification> getAllNotifications() {
        return notificationRepository.findAll();
    }
    
    // Get notification by ID
    public Optional<Notification> getNotificationById(String id) {
        return notificationRepository.findById(id);
    }
    
    // Get notifications by type
    public List<Notification> getNotificationsByType(String type) {
        return notificationRepository.findByType(type);
    }
    
    // Get notifications by audience
    public List<Notification> getNotificationsByAudience(String audience) {
        return notificationRepository.findByAudience(audience);
    }
    
    // Get notifications by status
    public List<Notification> getNotificationsByStatus(String status) {
        return notificationRepository.findByStatus(status);
    }
    
    // Get unread notifications for audience
    public List<Notification> getUnreadNotificationsForAudience(String audience) {
        return notificationRepository.findByAudienceAndReadOrderByTimestampDesc(audience, false);
    }
    
    // Get recent notifications
    public List<Notification> getRecentNotifications() {
        return notificationRepository.findTop10ByOrderByTimestampDesc();
    }
    
    // Get notifications after timestamp
    public List<Notification> getNotificationsAfterTimestamp(LocalDateTime timestamp) {
        return notificationRepository.findByTimestampAfter(timestamp);
    }
    
    // Mark notification as read
    public Notification markAsRead(String notificationId) {
        Optional<Notification> notificationOpt = notificationRepository.findById(notificationId);
        if (notificationOpt.isPresent()) {
            Notification notification = notificationOpt.get();
            notification.setRead(true);
            return notificationRepository.save(notification);
        }
        return null;
    }
    
    // Update notification status
    public Notification updateNotificationStatus(String notificationId, String status) {
        Optional<Notification> notificationOpt = notificationRepository.findById(notificationId);
        if (notificationOpt.isPresent()) {
            Notification notification = notificationOpt.get();
            notification.setStatus(status);
            if ("sent".equals(status)) {
                notification.setSentAt(LocalDateTime.now().toString());
            }
            return notificationRepository.save(notification);
        }
        return null;
    }
    
    // Create new notification
    public Notification createNotification(String title, String message, String type, String audience) {
        Notification notification = new Notification(title, message, type, audience);
        return notificationRepository.save(notification);
    }
    
    // Send notification
    public Notification sendNotification(String notificationId, Integer recipients) {
        Optional<Notification> notificationOpt = notificationRepository.findById(notificationId);
        if (notificationOpt.isPresent()) {
            Notification notification = notificationOpt.get();
            notification.setStatus("sent");
            notification.setRecipients(recipients);
            notification.setSentAt(LocalDateTime.now().toString());
            return notificationRepository.save(notification);
        }
        return null;
    }
    
    // Delete notification
    public void deleteNotification(String id) {
        notificationRepository.deleteById(id);
    }
    
    // Get notification statistics
    public long getTotalNotifications() {
        return notificationRepository.count();
    }
    
    public long getUnreadNotificationCount() {
        return notificationRepository.countByRead(false);
    }
    
    public long getSentNotificationCount() {
        return notificationRepository.countByStatus("sent");
    }
    
    public long getScheduledNotificationCount() {
        return notificationRepository.countByStatus("scheduled");
    }
    
    public long getDraftNotificationCount() {
        return notificationRepository.countByStatus("draft");
    }
}
