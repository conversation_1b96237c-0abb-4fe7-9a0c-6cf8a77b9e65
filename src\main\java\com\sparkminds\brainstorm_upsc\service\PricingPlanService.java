package com.sparkminds.brainstorm_upsc.service;

import com.sparkminds.brainstorm_upsc.entity.PricingPlan;
import com.sparkminds.brainstorm_upsc.repository.PricingPlanRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class PricingPlanService {
    
    @Autowired
    private PricingPlanRepository pricingPlanRepository;
    
    // Create or update pricing plan
    public PricingPlan savePricingPlan(PricingPlan pricingPlan) {
        return pricingPlanRepository.save(pricingPlan);
    }
    
    // Get all pricing plans
    public List<PricingPlan> getAllPricingPlans() {
        return pricingPlanRepository.findAll();
    }
    
    // Get pricing plan by ID
    public Optional<PricingPlan> getPricingPlanById(String id) {
        return pricingPlanRepository.findById(id);
    }
    
    // Get active pricing plans
    public List<PricingPlan> getActivePricingPlans() {
        return pricingPlanRepository.findByActiveOrderByPopularDesc(true);
    }
    
    // Get popular pricing plan
    public Optional<PricingPlan> getPopularPricingPlan() {
        return pricingPlanRepository.findByPopular(true);
    }
    
    // Get pricing plan by name
    public Optional<PricingPlan> getPricingPlanByName(String name) {
        return pricingPlanRepository.findByName(name);
    }
    
    // Update pricing plan status
    public PricingPlan updatePricingPlanStatus(String planId, Boolean active) {
        Optional<PricingPlan> planOpt = pricingPlanRepository.findById(planId);
        if (planOpt.isPresent()) {
            PricingPlan plan = planOpt.get();
            plan.setActive(active);
            return pricingPlanRepository.save(plan);
        }
        return null;
    }
    
    // Update popular status
    public PricingPlan updatePopularStatus(String planId, Boolean popular) {
        Optional<PricingPlan> planOpt = pricingPlanRepository.findById(planId);
        if (planOpt.isPresent()) {
            PricingPlan plan = planOpt.get();
            plan.setPopular(popular);
            return pricingPlanRepository.save(plan);
        }
        return null;
    }
    
    // Delete pricing plan
    public void deletePricingPlan(String id) {
        pricingPlanRepository.deleteById(id);
    }
    
    // Get pricing plan statistics
    public long getTotalPricingPlans() {
        return pricingPlanRepository.count();
    }
    
    public long getActivePricingPlanCount() {
        return pricingPlanRepository.findByActive(true).size();
    }
}
