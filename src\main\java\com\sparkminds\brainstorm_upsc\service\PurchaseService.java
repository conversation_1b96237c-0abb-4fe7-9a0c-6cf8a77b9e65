package com.sparkminds.brainstorm_upsc.service;

import com.sparkminds.brainstorm_upsc.entity.Purchase;
import com.sparkminds.brainstorm_upsc.repository.PurchaseRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class PurchaseService {
    
    @Autowired
    private PurchaseRepository purchaseRepository;
    
    // Create or update purchase
    public Purchase savePurchase(Purchase purchase) {
        return purchaseRepository.save(purchase);
    }
    
    // Get all purchases
    public List<Purchase> getAllPurchases() {
        return purchaseRepository.findAll();
    }
    
    // Get purchase by ID
    public Optional<Purchase> getPurchaseById(String id) {
        return purchaseRepository.findById(id);
    }
    
    // Get purchases by user ID
    public List<Purchase> getPurchasesByUserId(String userId) {
        return purchaseRepository.findByUserId(userId);
    }
    
    // Get purchases by material ID
    public List<Purchase> getPurchasesByMaterialId(String materialId) {
        return purchaseRepository.findByMaterialId(materialId);
    }
    
    // Get completed purchases by user
    public List<Purchase> getCompletedPurchasesByUser(String userId) {
        return purchaseRepository.findByUserIdAndStatusOrderByPurchaseDateDesc(userId, "completed");
    }
    
    // Check if user has purchased material
    public boolean hasUserPurchasedMaterial(String userId, String materialId) {
        Optional<Purchase> purchase = purchaseRepository.findUserMaterialPurchase(userId, materialId);
        return purchase.isPresent();
    }
    
    // Get purchase by payment ID
    public Optional<Purchase> getPurchaseByPaymentId(String paymentId) {
        return purchaseRepository.findByPaymentId(paymentId);
    }
    
    // Get purchase by order ID
    public Optional<Purchase> getPurchaseByOrderId(String orderId) {
        return purchaseRepository.findByOrderId(orderId);
    }
    
    // Get purchases by status
    public List<Purchase> getPurchasesByStatus(String status) {
        return purchaseRepository.findByStatus(status);
    }
    
    // Get purchases by payment method
    public List<Purchase> getPurchasesByPaymentMethod(String paymentMethod) {
        return purchaseRepository.findByPaymentMethod(paymentMethod);
    }
    
    // Get recent purchases
    public List<Purchase> getRecentPurchases() {
        return purchaseRepository.findTop10ByStatusOrderByPurchaseDateDesc("completed");
    }
    
    // Get purchases after date
    public List<Purchase> getPurchasesAfterDate(LocalDateTime date) {
        return purchaseRepository.findByPurchaseDateAfter(date);
    }
    
    // Get purchases between dates
    public List<Purchase> getPurchasesBetweenDates(LocalDateTime startDate, LocalDateTime endDate) {
        return purchaseRepository.findByPurchaseDateBetween(startDate, endDate);
    }
    
    // Get monthly purchases
    public List<Purchase> getMonthlyPurchases(LocalDateTime startOfMonth, LocalDateTime endOfMonth) {
        return purchaseRepository.findMonthlyPurchases(startOfMonth, endOfMonth);
    }
    
    // Get daily purchases
    public List<Purchase> getDailyPurchases(LocalDateTime startOfDay, LocalDateTime endOfDay) {
        return purchaseRepository.findDailyPurchases(startOfDay, endOfDay);
    }
    
    // Update purchase status
    public Purchase updatePurchaseStatus(String purchaseId, String status) {
        Optional<Purchase> purchaseOpt = purchaseRepository.findById(purchaseId);
        if (purchaseOpt.isPresent()) {
            Purchase purchase = purchaseOpt.get();
            purchase.setStatus(status);
            if ("completed".equals(status)) {
                purchase.setAccessGranted(true);
                purchase.setLastAccessedAt(LocalDateTime.now());
            }
            return purchaseRepository.save(purchase);
        }
        return null;
    }
    
    // Grant access to purchased material
    public Purchase grantAccess(String purchaseId) {
        Optional<Purchase> purchaseOpt = purchaseRepository.findById(purchaseId);
        if (purchaseOpt.isPresent()) {
            Purchase purchase = purchaseOpt.get();
            purchase.setAccessGranted(true);
            purchase.setLastAccessedAt(LocalDateTime.now());
            return purchaseRepository.save(purchase);
        }
        return null;
    }
    
    // Increment download count
    public Purchase incrementDownloadCount(String purchaseId) {
        Optional<Purchase> purchaseOpt = purchaseRepository.findById(purchaseId);
        if (purchaseOpt.isPresent()) {
            Purchase purchase = purchaseOpt.get();
            purchase.setDownloadCount(purchase.getDownloadCount() + 1);
            purchase.setLastAccessedAt(LocalDateTime.now());
            return purchaseRepository.save(purchase);
        }
        return null;
    }
    
    // Create new purchase
    public Purchase createPurchase(String userId, String materialId, Double amount, String paymentMethod) {
        Purchase purchase = new Purchase(userId, materialId, amount, paymentMethod);
        return purchaseRepository.save(purchase);
    }
    
    // Complete purchase
    public Purchase completePurchase(String purchaseId, String paymentId, String orderId) {
        Optional<Purchase> purchaseOpt = purchaseRepository.findById(purchaseId);
        if (purchaseOpt.isPresent()) {
            Purchase purchase = purchaseOpt.get();
            purchase.setPaymentId(paymentId);
            purchase.setOrderId(orderId);
            purchase.setStatus("completed");
            purchase.setAccessGranted(true);
            purchase.setLastAccessedAt(LocalDateTime.now());
            return purchaseRepository.save(purchase);
        }
        return null;
    }
    
    // Delete purchase
    public void deletePurchase(String id) {
        purchaseRepository.deleteById(id);
    }
    
    // Get purchase statistics
    public long getTotalPurchases() {
        return purchaseRepository.count();
    }
    
    public long getCompletedPurchaseCount() {
        return purchaseRepository.countByStatus("completed");
    }
    
    public long getPendingPurchaseCount() {
        return purchaseRepository.countByStatus("pending");
    }
    
    public long getFailedPurchaseCount() {
        return purchaseRepository.countByStatus("failed");
    }
    
    public long getPurchaseCountByUser(String userId) {
        return purchaseRepository.countByUserId(userId);
    }
    
    public long getPurchaseCountByMaterial(String materialId) {
        return purchaseRepository.countByMaterialId(materialId);
    }
    
    // Calculate total revenue
    public Double calculateTotalRevenue() {
        List<Purchase> completedPurchases = purchaseRepository.findCompletedPurchasesForRevenue();
        return completedPurchases.stream()
                .mapToDouble(Purchase::getAmount)
                .sum();
    }
}
