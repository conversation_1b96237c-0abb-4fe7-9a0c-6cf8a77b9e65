package com.sparkminds.brainstorm_upsc.service;

import com.sparkminds.brainstorm_upsc.entity.Question;
import com.sparkminds.brainstorm_upsc.repository.QuestionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class QuestionService {
    
    @Autowired
    private QuestionRepository questionRepository;
    
    // Create or update question
    public Question saveQuestion(Question question) {
        return questionRepository.save(question);
    }
    
    // Get all questions
    public List<Question> getAllQuestions() {
        return questionRepository.findAll();
    }
    
    // Get question by ID
    public Optional<Question> getQuestionById(String id) {
        return questionRepository.findById(id);
    }
    
    // Get questions by exam ID
    public List<Question> getQuestionsByExamId(String examId) {
        return questionRepository.findByExamId(examId);
    }
    
    // Search questions by text
    public List<Question> searchQuestions(String searchTerm) {
        return questionRepository.searchByQuestionText(searchTerm);
    }
    
    // Get questions by explanation containing text
    public List<Question> getQuestionsByExplanation(String text) {
        return questionRepository.findByExplanationContaining(text);
    }
    
    // Get questions by correct answer index
    public List<Question> getQuestionsByCorrectAnswer(Integer correctIndex) {
        return questionRepository.findByCorrect(correctIndex);
    }
    
    // Get questions for multiple exams
    public List<Question> getQuestionsByExamIds(List<String> examIds) {
        return questionRepository.findByExamIdIn(examIds);
    }
    
    // Delete question
    public void deleteQuestion(String id) {
        questionRepository.deleteById(id);
    }
    
    // Delete all questions for an exam
    public void deleteQuestionsByExamId(String examId) {
        questionRepository.deleteByExamId(examId);
    }
    
    // Get question count by exam
    public long getQuestionCountByExam(String examId) {
        return questionRepository.countByExamId(examId);
    }
    
    // Get total questions
    public long getTotalQuestions() {
        return questionRepository.count();
    }
    
    // Save multiple questions
    public List<Question> saveAllQuestions(List<Question> questions) {
        return questionRepository.saveAll(questions);
    }
    
    // Update question
    public Question updateQuestion(String questionId, String questionText, 
                                 List<String> options, Integer correct, String explanation) {
        Optional<Question> questionOpt = questionRepository.findById(questionId);
        if (questionOpt.isPresent()) {
            Question question = questionOpt.get();
            question.setQuestion(questionText);
            question.setOptions(options);
            question.setCorrect(correct);
            question.setExplanation(explanation);
            return questionRepository.save(question);
        }
        return null;
    }
}
