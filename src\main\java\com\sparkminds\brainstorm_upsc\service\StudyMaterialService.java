package com.sparkminds.brainstorm_upsc.service;

import com.sparkminds.brainstorm_upsc.entity.StudyMaterial;
import com.sparkminds.brainstorm_upsc.repository.StudyMaterialRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Service
public class StudyMaterialService {
    
    @Autowired
    private StudyMaterialRepository studyMaterialRepository;
    
    // Create or update study material
    public StudyMaterial saveMaterial(StudyMaterial material) {
        return studyMaterialRepository.save(material);
    }
    
    // Get all materials
    public List<StudyMaterial> getAllMaterials() {
        return studyMaterialRepository.findAll();
    }
    
    // Get material by ID
    public Optional<StudyMaterial> getMaterialById(String id) {
        return studyMaterialRepository.findById(id);
    }
    
    // Get published materials
    public List<StudyMaterial> getPublishedMaterials() {
        return studyMaterialRepository.findByStatusOrderByCreatedAtDesc("published");
    }
    
    // Get materials by type
    public List<StudyMaterial> getMaterialsByType(String type) {
        return studyMaterialRepository.findByTypeAndStatus(type, "published");
    }
    
    // Get materials by subject
    public List<StudyMaterial> getMaterialsBySubject(String subject) {
        return studyMaterialRepository.findBySubjectAndStatus(subject, "published");
    }
    
    // Get premium materials
    public List<StudyMaterial> getPremiumMaterials() {
        return studyMaterialRepository.findPremiumMaterials();
    }
    
    // Get free materials
    public List<StudyMaterial> getFreeMaterials() {
        return studyMaterialRepository.findFreeMaterials();
    }
    
    // Get materials with pagination
    public Page<StudyMaterial> getMaterialsWithPagination(Pageable pageable) {
        return studyMaterialRepository.findByStatus("published", pageable);
    }
    
    // Get materials by type with pagination
    public Page<StudyMaterial> getMaterialsByTypeWithPagination(String type, Pageable pageable) {
        return studyMaterialRepository.findByTypeAndStatus(type, "published", pageable);
    }
    
    // Get materials by subject with pagination
    public Page<StudyMaterial> getMaterialsBySubjectWithPagination(String subject, Pageable pageable) {
        return studyMaterialRepository.findBySubjectAndStatus(subject, "published", pageable);
    }
    
    // Search materials
    public List<StudyMaterial> searchMaterials(String searchTerm) {
        return studyMaterialRepository.searchByTitleOrDescription(searchTerm);
    }
    
    // Get popular materials
    public List<StudyMaterial> getPopularMaterials(Double minRating, Integer minReviews) {
        return studyMaterialRepository.findPopularMaterials(minRating, minReviews);
    }
    
    // Get top rated materials
    public List<StudyMaterial> getTopRatedMaterials() {
        return studyMaterialRepository.findTop10ByStatusOrderByRatingDesc("published");
    }
    
    // Get most downloaded materials
    public List<StudyMaterial> getMostDownloadedMaterials() {
        return studyMaterialRepository.findTop10ByStatusOrderByDownloadsDesc("published");
    }
    
    // Get recent materials
    public List<StudyMaterial> getRecentMaterials() {
        return studyMaterialRepository.findTop10ByStatusOrderByCreatedAtDesc("published");
    }
    
    // Get materials by price range
    public List<StudyMaterial> getMaterialsByPriceRange(Double minPrice, Double maxPrice) {
        return studyMaterialRepository.findByPriceBetween(minPrice, maxPrice);
    }
    
    // Get materials by author
    public List<StudyMaterial> getMaterialsByAuthor(String author) {
        return studyMaterialRepository.findByAuthor(author);
    }
    
    // Get materials created after date
    public List<StudyMaterial> getMaterialsCreatedAfter(LocalDate date) {
        return studyMaterialRepository.findByCreatedAtAfter(date);
    }
    
    // Update material status
    public StudyMaterial updateMaterialStatus(String materialId, String status) {
        Optional<StudyMaterial> materialOpt = studyMaterialRepository.findById(materialId);
        if (materialOpt.isPresent()) {
            StudyMaterial material = materialOpt.get();
            material.setStatus(status);
            return studyMaterialRepository.save(material);
        }
        return null;
    }
    
    // Update download count
    public StudyMaterial incrementDownloadCount(String materialId) {
        Optional<StudyMaterial> materialOpt = studyMaterialRepository.findById(materialId);
        if (materialOpt.isPresent()) {
            StudyMaterial material = materialOpt.get();
            material.setDownloads(material.getDownloads() + 1);
            return studyMaterialRepository.save(material);
        }
        return null;
    }
    
    // Update rating and reviews
    public StudyMaterial updateRatingAndReviews(String materialId, Double rating, Integer reviews) {
        Optional<StudyMaterial> materialOpt = studyMaterialRepository.findById(materialId);
        if (materialOpt.isPresent()) {
            StudyMaterial material = materialOpt.get();
            material.setRating(rating);
            material.setReviews(reviews);
            return studyMaterialRepository.save(material);
        }
        return null;
    }
    
    // Delete material
    public void deleteMaterial(String id) {
        studyMaterialRepository.deleteById(id);
    }
    
    // Get material statistics
    public long getTotalMaterials() {
        return studyMaterialRepository.count();
    }
    
    public long getPublishedMaterialCount() {
        return studyMaterialRepository.countByStatus("published");
    }
    
    public long getDraftMaterialCount() {
        return studyMaterialRepository.countByStatus("draft");
    }
    
    public long getPremiumMaterialCount() {
        return studyMaterialRepository.countByIsPremium(true);
    }
    
    public long getFreeMaterialCount() {
        return studyMaterialRepository.countByIsPremium(false);
    }
    
    public long getMaterialCountByType(String type) {
        return studyMaterialRepository.countByType(type);
    }
    
    public long getMaterialCountBySubject(String subject) {
        return studyMaterialRepository.countBySubject(subject);
    }
}
