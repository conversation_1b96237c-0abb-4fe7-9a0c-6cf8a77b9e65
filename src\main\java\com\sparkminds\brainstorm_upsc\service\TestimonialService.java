package com.sparkminds.brainstorm_upsc.service;

import com.sparkminds.brainstorm_upsc.entity.Testimonial;
import com.sparkminds.brainstorm_upsc.repository.TestimonialRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class TestimonialService {
    
    @Autowired
    private TestimonialRepository testimonialRepository;
    
    // Create or update testimonial
    public Testimonial saveTestimonial(Testimonial testimonial) {
        return testimonialRepository.save(testimonial);
    }
    
    // Get all testimonials
    public List<Testimonial> getAllTestimonials() {
        return testimonialRepository.findAllByOrderByFeaturedDesc();
    }
    
    // Get testimonial by ID
    public Optional<Testimonial> getTestimonialById(String id) {
        return testimonialRepository.findById(id);
    }
    
    // Get featured testimonials
    public List<Testimonial> getFeaturedTestimonials() {
        return testimonialRepository.findByFeatured(true);
    }
    
    // Search testimonials by name
    public List<Testimonial> searchTestimonialsByName(String name) {
        return testimonialRepository.findByNameContainingIgnoreCase(name);
    }
    
    // Update featured status
    public Testimonial updateFeaturedStatus(String testimonialId, Boolean featured) {
        Optional<Testimonial> testimonialOpt = testimonialRepository.findById(testimonialId);
        if (testimonialOpt.isPresent()) {
            Testimonial testimonial = testimonialOpt.get();
            testimonial.setFeatured(featured);
            return testimonialRepository.save(testimonial);
        }
        return null;
    }
    
    // Delete testimonial
    public void deleteTestimonial(String id) {
        testimonialRepository.deleteById(id);
    }
    
    // Get testimonial statistics
    public long getTotalTestimonials() {
        return testimonialRepository.count();
    }
    
    public long getFeaturedTestimonialCount() {
        return testimonialRepository.findByFeatured(true).size();
    }
}
