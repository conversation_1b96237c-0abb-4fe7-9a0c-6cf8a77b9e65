package com.sparkminds.brainstorm_upsc.service;

import com.sparkminds.brainstorm_upsc.entity.User;
import com.sparkminds.brainstorm_upsc.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Service
public class UserService {
    
    @Autowired
    private UserRepository userRepository;
    
    // Create or update user
    public User saveUser(User user) {
        return userRepository.save(user);
    }
    
    // Get all users
    public List<User> getAllUsers() {
        return userRepository.findAll();
    }
    
    // Get user by ID
    public Optional<User> getUserById(String id) {
        return userRepository.findById(id);
    }
    
    // Get user by email
    public Optional<User> getUserByEmail(String email) {
        return userRepository.findByEmail(email);
    }
    
    // Authenticate user
    public Optional<User> authenticateUser(String email, String password) {
        return userRepository.findByEmailAndPassword(email, password);
    }
    
    // Get users by role
    public List<User> getUsersByRole(String role) {
        return userRepository.findByRole(role);
    }
    
    // Get users by subscription
    public List<User> getUsersBySubscription(String subscription) {
        return userRepository.findBySubscription(subscription);
    }
    
    // Get active users
    public List<User> getActiveUsers() {
        return userRepository.findByStatus("active");
    }
    
    // Get students
    public List<User> getStudents() {
        return userRepository.findByRole("student");
    }
    
    // Get admins
    public List<User> getAdmins() {
        return userRepository.findByRole("admin");
    }
    
    // Get premium users
    public List<User> getPremiumUsers() {
        return userRepository.findBySubscription("premium");
    }
    
    // Get users joined after specific date
    public List<User> getUsersJoinedAfter(LocalDate date) {
        return userRepository.findByJoinedAtAfter(date);
    }
    
    // Get top performers
    public List<User> getTopPerformers() {
        return userRepository.findTop10ByOrderByAvgScoreDesc();
    }
    
    // Get recently active users
    public List<User> getRecentlyActiveUsers(LocalDate date) {
        return userRepository.findByLastActiveAfter(date);
    }
    
    // Search users by name
    public List<User> searchUsersByName(String name) {
        return userRepository.findByNameContainingIgnoreCase(name);
    }
    
    // Update user subscription
    public User updateUserSubscription(String userId, String subscription) {
        Optional<User> userOpt = userRepository.findById(userId);
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            user.setSubscription(subscription);
            return userRepository.save(user);
        }
        return null;
    }
    
    // Update user status
    public User updateUserStatus(String userId, String status) {
        Optional<User> userOpt = userRepository.findById(userId);
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            user.setStatus(status);
            user.setLastActive(LocalDate.now());
            return userRepository.save(user);
        }
        return null;
    }
    
    // Update user exam stats
    public User updateUserExamStats(String userId, Integer examsCompleted, Double avgScore) {
        Optional<User> userOpt = userRepository.findById(userId);
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            user.setExamsCompleted(examsCompleted);
            user.setAvgScore(avgScore);
            user.setLastActive(LocalDate.now());
            return userRepository.save(user);
        }
        return null;
    }
    
    // Check if email exists
    public boolean emailExists(String email) {
        return userRepository.existsByEmail(email);
    }
    
    // Delete user
    public void deleteUser(String id) {
        userRepository.deleteById(id);
    }
    
    // Get user statistics
    public long getTotalUsers() {
        return userRepository.count();
    }
    
    public long getStudentCount() {
        return userRepository.countByRole("student");
    }
    
    public long getAdminCount() {
        return userRepository.countByRole("admin");
    }
    
    public long getActiveUserCount() {
        return userRepository.countByStatus("active");
    }
    
    public long getPremiumUserCount() {
        return userRepository.countBySubscription("premium");
    }
    
    public long getFreeUserCount() {
        return userRepository.countBySubscription("free");
    }
    
    // Register new user
    public User registerUser(String name, String email, String password, String phone) {
        if (emailExists(email)) {
            throw new RuntimeException("Email already exists");
        }
        
        User user = new User(name, email, password, phone, "student");
        return userRepository.save(user);
    }
    
    // Update last active
    public void updateLastActive(String userId) {
        Optional<User> userOpt = userRepository.findById(userId);
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            user.setLastActive(LocalDate.now());
            userRepository.save(user);
        }
    }

    public List<User> getUsersByStatus(String status) {
        return List.of();
    }
}
