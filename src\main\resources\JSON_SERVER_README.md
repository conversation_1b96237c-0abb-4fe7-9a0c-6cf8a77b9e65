# 🗄️ JSON Server Database Documentation

## 📋 **Overview**
This document provides comprehensive details about the JSON Server database (`db.json`) used in the Brainstorm UPSC platform. The database serves as a mock backend API for development and testing purposes.

---

## 🚀 **Quick Start**

### **Starting JSON Server**
```bash
# Install JSON Server globally (if not already installed)
npm install -g json-server

# Start the server
npm run server
# OR
json-server --watch db.json --port 3001
```

### **Server Information**
- **URL**: http://localhost:3001
- **Port**: 3001
- **Database File**: `db.json`
- **Auto-reload**: Yes (watches file changes)

---

## 📊 **Database Structure**

### **Collections Overview**
| Collection | Records | Description |
|------------|---------|-------------|
| `users` | 4 | User accounts (students, admins) |
| `studyMaterials` | 5 | Educational content (PDFs, videos, audio) |
| `exams` | 4 | Mock tests and practice exams |
| `questions` | 3 | Exam questions with answers |
| `testimonials` | 3 | User success stories |
| `pricingPlans` | 3 | Subscription plans |
| `purchases` | 4 | Payment and purchase records |
| `notifications` | 3 | System notifications |
| `adminStats` | 4 | Dashboard statistics |
| `userGrowthData` | 6 | User growth analytics |
| `progressData` | 6 | User progress analytics |
| `upcomingExams` | 3 | Scheduled exams |
| `recentMaterials` | 3 | Recently added materials |

---

## 👥 **Users Collection**

### **Endpoint**: `/users`
```json
{
  "id": "1",
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "phone": "+91 **********",
  "role": "student",
  "subscription": "premium",
  "joinedAt": "2024-01-15",
  "lastActive": "2025-07-09",
  "status": "active",
  "examsCompleted": 15,
  "avgScore": 85,
  "avatar": "https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg"
}
```

### **User Roles**
- **student**: Regular users who can purchase materials and take exams
- **admin**: Administrative users with full access

### **Test Accounts**
| Email | Password | Role | Subscription |
|-------|----------|------|--------------|
| `<EMAIL>` | `password123` | student | premium |
| `<EMAIL>` | `admin123` | admin | premium |
| `<EMAIL>` | `password123` | student | free |
| `<EMAIL>` | `password123` | student | premium |

---

## 📚 **Study Materials Collection**

### **Endpoint**: `/studyMaterials`
```json
{
  "id": "1",
  "title": "Current Affairs March 2024",
  "description": "Comprehensive current affairs compilation with 500+ questions",
  "type": "PDF",
  "subject": "Current Affairs",
  "price": 299,
  "originalPrice": 399,
  "rating": 4.8,
  "reviews": 245,
  "pages": 150,
  "author": "Dr. Rajesh Kumar",
  "isPremium": true,
  "thumbnailUrl": "https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg",
  "status": "published",
  "downloads": 245,
  "createdAt": "2024-03-15",
  "fileSize": "15.2 MB"
}
```

### **Material Types**
- **PDF**: Document-based study materials
- **Video**: Video lectures and courses
- **Audio**: Audio lectures for mobile learning

### **Subjects Available**
- Current Affairs
- Polity
- Economics
- Geography
- History

---

## 📝 **Exams Collection**

### **Endpoint**: `/exams`
```json
{
  "id": "1",
  "title": "UPSC Prelims Mock Test 15",
  "description": "Full-length mock test covering all subjects",
  "type": "Mock Test",
  "duration": 120,
  "questions": 100,
  "participants": 1250,
  "difficulty": "Hard",
  "status": "published",
  "startDate": "2024-03-20",
  "startTime": "10:00 AM",
  "createdAt": "2024-03-15",
  "scheduledAt": "2024-03-20 10:00",
  "creator": "Admin"
}
```

### **Exam Types**
- **Mock Test**: Full-length practice exams
- **Subject Test**: Subject-specific tests
- **Current Affairs**: Current affairs focused tests

### **Difficulty Levels**
- **Easy**: Beginner level
- **Medium**: Intermediate level
- **Hard**: Advanced level

---

## ❓ **Questions Collection**

### **Endpoint**: `/questions`
```json
{
  "id": "1",
  "examId": 1,
  "question": "Which of the following is considered the most important source of information about the Mauryan administration?",
  "options": [
    "Arthashastra",
    "Indica",
    "Mudrarakshasa",
    "Mahavamsa"
  ],
  "correct": 0,
  "explanation": "Arthashastra by Kautilya is the most comprehensive source of information about Mauryan administration."
}
```

### **Question Structure**
- **examId**: Links to specific exam
- **options**: Array of 4 multiple choice options
- **correct**: Index of correct answer (0-based)
- **explanation**: Detailed explanation of the answer

---

## 💳 **Purchases Collection**

### **Endpoint**: `/purchases`
```json
{
  "id": 1752042900260,
  "userId": 1,
  "materialId": "1",
  "paymentId": "order_1_1_1752042857311",
  "orderId": "order_1_1_1752042857311",
  "amount": 299,
  "currency": "INR",
  "paymentMethod": "cashfree",
  "status": "completed",
  "purchaseDate": "2025-07-09T06:35:00.260Z",
  "accessGranted": true,
  "downloadCount": 0,
  "lastAccessedAt": "2025-07-09T06:35:00.260Z"
}
```

### **Payment Methods**
- **razorpay**: Primary payment gateway
- **cashfree**: Fallback payment gateway
- **upi**: UPI payments

### **Purchase Status**
- **completed**: Successful purchase
- **pending**: Payment in progress
- **failed**: Failed payment

---

## 🔔 **Notifications Collection**

### **Endpoint**: `/notifications`
```json
{
  "id": "1",
  "title": "New Weekly Exam Available",
  "message": "UPSC Prelims Mock Test 15 is now available. Don't miss the deadline!",
  "type": "info",
  "timestamp": "2024-03-19T10:00:00.000Z",
  "read": false,
  "audience": "all",
  "status": "sent",
  "sentAt": "2024-03-18 10:00",
  "recipients": 2847,
  "openRate": 78
}
```

### **Notification Types**
- **info**: General information
- **success**: Success messages
- **warning**: Warning messages
- **error**: Error messages

### **Audience Types**
- **all**: All users
- **premium**: Premium subscribers only
- **students**: Student users only
- **admins**: Admin users only

---

## 📈 **Analytics Collections**

### **Admin Stats** (`/adminStats`)
Dashboard statistics for admin panel:
- Total Users
- Active Exams
- Study Materials
- Monthly Revenue

### **User Growth Data** (`/userGrowthData`)
Monthly user growth analytics for charts and graphs.

### **Progress Data** (`/progressData`)
User progress and score analytics over time.

---

## 🔗 **API Endpoints**

### **Base URL**: `http://localhost:3001`

### **Standard REST Operations**
```bash
# Get all records
GET /users
GET /studyMaterials
GET /exams

# Get single record
GET /users/1
GET /studyMaterials/1
GET /exams/1

# Create new record
POST /users
POST /studyMaterials
POST /purchases

# Update record
PUT /users/1
PATCH /studyMaterials/1

# Delete record
DELETE /users/1
DELETE /exams/1
```

### **Query Parameters**
```bash
# Filter by field
GET /users?role=student
GET /studyMaterials?type=PDF
GET /exams?status=published

# Sort records
GET /users?_sort=name&_order=asc
GET /studyMaterials?_sort=price&_order=desc

# Pagination
GET /users?_page=1&_limit=10

# Search
GET /studyMaterials?q=current affairs

# Relationships
GET /users/1/purchases
GET /exams/1/questions
```

---

## 🛠️ **Development Usage**

### **Authentication Simulation**
```javascript
// Login simulation
const loginUser = async (email, password) => {
  const response = await fetch('http://localhost:3001/users?email=' + email);
  const users = await response.json();
  const user = users.find(u => u.password === password);
  return user || null;
};
```

### **Purchase Simulation**
```javascript
// Record purchase
const recordPurchase = async (purchaseData) => {
  const response = await fetch('http://localhost:3001/purchases', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(purchaseData)
  });
  return response.json();
};
```

### **Material Access Check**
```javascript
// Check if user has purchased material
const hasPurchased = async (userId, materialId) => {
  const response = await fetch(`http://localhost:3001/purchases?userId=${userId}&materialId=${materialId}`);
  const purchases = await response.json();
  return purchases.length > 0 && purchases[0].status === 'completed';
};
```

---

## 🔧 **Configuration**

### **JSON Server Options**
```json
{
  "port": 3001,
  "watch": true,
  "static": "./public",
  "delay": 0,
  "middlewares": [],
  "routes": "./routes.json"
}
```

### **Custom Routes** (Optional)
Create `routes.json` for custom URL mappings:
```json
{
  "/api/*": "/$1",
  "/materials": "/studyMaterials",
  "/tests": "/exams"
}
```

---

## 📝 **Data Validation**

### **Required Fields**
- **Users**: name, email, password, role
- **Study Materials**: title, description, type, subject, price
- **Exams**: title, description, type, duration, questions
- **Purchases**: userId, materialId, amount, paymentMethod

### **Data Types**
- **IDs**: String or Number
- **Prices**: Number (in paise for Razorpay, rupees for display)
- **Dates**: ISO 8601 format strings
- **Status**: Predefined enum values

---

## 🚀 **Production Notes**

### **Migration to Real Database**
When moving to production:
1. Replace JSON Server with PostgreSQL/MongoDB
2. Implement proper authentication and authorization
3. Add data validation and sanitization
4. Set up proper indexing for performance
5. Implement backup and recovery procedures

### **Security Considerations**
- Passwords are stored in plain text (development only)
- No authentication middleware
- No rate limiting
- No input validation

**⚠️ Warning**: This setup is for development only. Never use in production!

---

## 📊 **Current Data Summary**

- **Total Users**: 4 (1 admin, 3 students)
- **Study Materials**: 5 items across 5 subjects
- **Mock Exams**: 4 tests with varying difficulty
- **Sample Questions**: 3 questions with explanations
- **Purchase Records**: 4 completed transactions
- **Test Accounts**: Ready for immediate testing

---

## 🔍 **Advanced Usage Examples**

### **Complex Queries**
```bash
# Get premium materials with high ratings
GET /studyMaterials?isPremium=true&rating_gte=4.5

# Get recent exams (last 30 days)
GET /exams?createdAt_gte=2024-02-20

# Get user purchases with material details
GET /purchases?_expand=user&_expand=studyMaterial

# Get exams with question count
GET /exams?_embed=questions

# Search materials by title or description
GET /studyMaterials?q=current affairs

# Get paginated results
GET /studyMaterials?_page=1&_limit=5&_sort=createdAt&_order=desc
```

### **Bulk Operations**
```javascript
// Add multiple purchases
const bulkPurchases = [
  { userId: 1, materialId: "1", amount: 299, paymentMethod: "razorpay", status: "completed" },
  { userId: 1, materialId: "2", amount: 599, paymentMethod: "cashfree", status: "completed" }
];

bulkPurchases.forEach(async (purchase) => {
  await fetch('http://localhost:3001/purchases', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(purchase)
  });
});
```

---

## 🛠️ **Troubleshooting**

### **Common Issues**

#### **Port Already in Use**
```bash
# Error: Port 3001 is already in use
# Solution: Kill the process or use different port
npx kill-port 3001
# OR
json-server --watch db.json --port 3002
```

#### **CORS Issues**
```bash
# Add CORS headers
json-server --watch db.json --port 3001 --middlewares ./cors.js
```

Create `cors.js`:
```javascript
module.exports = (req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', '*');
  res.header('Access-Control-Allow-Methods', '*');
  next();
};
```

#### **File Watch Issues**
```bash
# If auto-reload not working
json-server --watch db.json --port 3001 --no-cors --delay 1000
```

### **Performance Tips**
- Use pagination for large datasets (`_page` & `_limit`)
- Implement client-side caching for static data
- Use specific field queries instead of fetching all data
- Consider using `_embed` and `_expand` for related data

---

## 📋 **Backup & Restore**

### **Backup Database**
```bash
# Create backup with timestamp
cp db.json "backups/db_backup_$(date +%Y%m%d_%H%M%S).json"

# Automated backup script
#!/bin/bash
BACKUP_DIR="backups"
mkdir -p $BACKUP_DIR
cp db.json "$BACKUP_DIR/db_backup_$(date +%Y%m%d_%H%M%S).json"
echo "Backup created successfully"
```

### **Restore Database**
```bash
# Restore from backup
cp backups/db_backup_20240320_143000.json db.json

# Reset to initial state
git checkout db.json
```

---

## 🔄 **Data Migration Scripts**

### **Add New Fields to Existing Records**
```javascript
// migrate.js - Add new fields to all users
const fs = require('fs');
const db = JSON.parse(fs.readFileSync('db.json', 'utf8'));

// Add new field to all users
db.users = db.users.map(user => ({
  ...user,
  profileCompleted: user.profileCompleted || false,
  preferences: user.preferences || {
    emailNotifications: true,
    smsNotifications: false
  }
}));

fs.writeFileSync('db.json', JSON.stringify(db, null, 2));
console.log('Migration completed');
```

### **Data Seeding Script**
```javascript
// seed.js - Add sample data
const fs = require('fs');
const db = JSON.parse(fs.readFileSync('db.json', 'utf8'));

// Add sample study materials
const newMaterials = [
  {
    id: "6",
    title: "Science & Technology Digest",
    description: "Latest developments in science and technology",
    type: "PDF",
    subject: "Science & Technology",
    price: 249,
    originalPrice: 349,
    rating: 4.4,
    reviews: 89,
    pages: 120,
    author: "Dr. Tech Expert",
    isPremium: true,
    thumbnailUrl: "https://example.com/image.jpg",
    status: "published",
    downloads: 89,
    createdAt: new Date().toISOString().split('T')[0],
    fileSize: "12.5 MB"
  }
];

db.studyMaterials.push(...newMaterials);
fs.writeFileSync('db.json', JSON.stringify(db, null, 2));
```

---

## 📊 **Monitoring & Analytics**

### **Server Logs**
```bash
# Run with verbose logging
json-server --watch db.json --port 3001 --verbose

# Log requests to file
json-server --watch db.json --port 3001 > server.log 2>&1
```

### **Request Analytics**
```javascript
// analytics.js - Track API usage
const fs = require('fs');
const path = require('path');

module.exports = (req, res, next) => {
  const logEntry = {
    timestamp: new Date().toISOString(),
    method: req.method,
    url: req.url,
    userAgent: req.get('User-Agent'),
    ip: req.ip
  };

  const logFile = path.join(__dirname, 'api-usage.log');
  fs.appendFileSync(logFile, JSON.stringify(logEntry) + '\n');

  next();
};
```

---

## 🎯 **Testing Scenarios**

### **User Authentication Flow**
```javascript
// Test complete auth flow
describe('Authentication', () => {
  test('should login with valid credentials', async () => {
    const response = await fetch('http://localhost:3001/users?email=<EMAIL>');
    const users = await response.json();
    expect(users[0].password).toBe('password123');
  });
});
```

### **Purchase Flow Testing**
```javascript
// Test purchase workflow
describe('Purchase Flow', () => {
  test('should record purchase successfully', async () => {
    const purchaseData = {
      userId: 1,
      materialId: "1",
      amount: 299,
      paymentMethod: "razorpay",
      status: "completed",
      purchaseDate: new Date().toISOString()
    };

    const response = await fetch('http://localhost:3001/purchases', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(purchaseData)
    });

    expect(response.status).toBe(201);
  });
});
```

---

## 🚀 **Production Migration Checklist**

### **Database Migration**
- [ ] Export data from JSON Server
- [ ] Set up production database (PostgreSQL/MongoDB)
- [ ] Create proper database schema
- [ ] Import and validate data
- [ ] Set up database indexes
- [ ] Configure backup procedures

### **API Migration**
- [ ] Replace JSON Server with Express.js/Fastify
- [ ] Implement proper authentication (JWT)
- [ ] Add input validation and sanitization
- [ ] Set up rate limiting
- [ ] Add API documentation (Swagger)
- [ ] Implement proper error handling

### **Security Enhancements**
- [ ] Hash passwords with bcrypt
- [ ] Implement RBAC (Role-Based Access Control)
- [ ] Add HTTPS/SSL certificates
- [ ] Set up CORS properly
- [ ] Implement API key management
- [ ] Add request logging and monitoring

The JSON Server provides a complete mock backend for developing and testing the Brainstorm UPSC platform! 🎯📚
