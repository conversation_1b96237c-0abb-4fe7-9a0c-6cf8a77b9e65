# Application Configuration
spring.application.name=brainstorm-upsc

# Server Configuration
server.port=8080
server.servlet.context-path=/api

# MongoDB Configuration
spring.data.mongodb.host=localhost
spring.data.mongodb.port=27017
spring.data.mongodb.database=brainstorm_upsc
spring.data.mongodb.auto-index-creation=true

# Jackson Configuration
spring.jackson.serialization.write-dates-as-timestamps=false
spring.jackson.time-zone=Asia/Kolkata

# Logging Configuration
logging.level.com.sparkminds.brainstorm_upsc=DEBUG
logging.level.org.springframework.data.mongodb=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# Application Information
app.name=Brainstorm UPSC Backend
app.version=1.0.0
app.description=Spring Boot backend for Brainstorm UPSC platform

# CORS Configuration (handled in CorsConfig.java)
cors.allowed-origins=*
cors.allowed-methods=GET,POST,PUT,PATCH,DELETE,OPTIONS
cors.allowed-headers=*
cors.allow-credentials=true
cors.max-age=3600

# Swagger/OpenAPI Configuration
springdoc.api-docs.path=/v3/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.enabled=true
